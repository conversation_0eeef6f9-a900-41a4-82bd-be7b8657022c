// Category constants for Hive Campus
// This file centralizes all category definitions to ensure consistency across the app

export interface CategoryOption {
  id: string;
  name: string;
  backendValue: string; // Maps to backend enum values
}

// Updated category options with new structure
export const categoryOptions: CategoryOption[] = [
  // 🎮 Tech & Electronics
  { id: 'electronics', name: 'Electronics & Tech', backendValue: 'electronics' },
  { id: 'phones', name: 'Mobile Phones', backendValue: 'electronics' },
  { id: 'computers', name: 'Computers & Laptops', backendValue: 'electronics' },
  { id: 'gaming', name: 'Gaming & Consoles', backendValue: 'electronics' },
  { id: 'audio', name: 'Audio & Headphones', backendValue: 'electronics' },
  { id: 'cameras', name: 'Cameras & Photography', backendValue: 'electronics' },

  // 📚 Academic
  { id: 'textbooks', name: 'Textbooks & Education', backendValue: 'textbooks' },

  // 👗 Fashion
  { id: 'clothing', name: 'Clothing & Fashion', backendValue: 'clothing' },
  { id: 'shoes', name: 'Shoes & Sneakers', backendValue: 'clothing' },
  { id: 'accessories', name: 'Accessories & Jewelry', backendValue: 'clothing' },
  { id: 'bags', name: 'Bags & Backpacks', backendValue: 'clothing' },

  // 🛋️ Lifestyle
  { id: 'furniture', name: 'Furniture & Decor', backendValue: 'furniture' },

  // ✅ Existing Categories (merge to keep backward compatibility)
  { id: 'dorm', name: 'Dorm Essentials', backendValue: 'other' },
  { id: 'school', name: 'School Supplies', backendValue: 'other' },
  { id: 'tickets', name: 'Tickets', backendValue: 'other' },
  { id: 'bikes', name: 'Bikes & Scooters', backendValue: 'other' },
  { id: 'sublets', name: 'Sublets & Rentals', backendValue: 'other' },
  { id: 'services', name: 'Services', backendValue: 'other' },
  { id: 'tutoring', name: 'Tutoring', backendValue: 'other' },
  { id: 'events', name: 'Event Passes', backendValue: 'other' },
  { id: 'hobbies', name: 'Hobbies & Games', backendValue: 'other' },
  { id: 'beauty', name: 'Beauty & Personal Care', backendValue: 'other' },
  { id: 'food', name: 'Food & Meal Swipes', backendValue: 'other' },
  { id: 'lab', name: 'Lab Equipment', backendValue: 'other' },
  { id: 'instruments', name: 'Instruments', backendValue: 'other' },
  { id: 'gym', name: 'Gym Equipment', backendValue: 'other' },
  { id: 'other', name: 'Other', backendValue: 'other' },
];

// For search page - includes "All Categories" option
export const searchCategories = [
  { id: 'all', name: 'All Categories', backendValue: 'all' },
  ...categoryOptions
];

// Simple array of category names for create/edit listing dropdowns
export const categoryNames = categoryOptions.map(cat => cat.name);

// Helper function to get backend value from display name
export const getBackendCategoryValue = (displayName: string): string => {
  const category = categoryOptions.find(cat => cat.name === displayName);
  return category?.backendValue || 'other';
};

// Helper function to get display name from backend value
export const getDisplayCategoryName = (backendValue: string): string => {
  const category = categoryOptions.find(cat => cat.backendValue === backendValue);
  return category?.name || backendValue;
};

// Helper function to get category by ID
export const getCategoryById = (id: string): CategoryOption | undefined => {
  return categoryOptions.find(cat => cat.id === id);
};
