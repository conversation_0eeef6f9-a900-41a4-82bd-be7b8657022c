import { useState, useEffect, useCallback } from 'react';
import { useAuth } from './useAuth';
import { httpsCallable } from 'firebase/functions';
import { functions } from '../firebase/config';
import { AdminDataService } from '../services/AdminDataService';

export interface AdminDashboardStats {
  users: {
    total: number;
    active: number;
    banned: number;
    verified: number;
    recentGrowth: number;
  };
  listings: {
    total: number;
    active: number;
    pending: number;
    recentGrowth: number;
  };
  orders: {
    total: number;
    completed: number;
    revenue: number;
    averageOrderValue: number;
  };
  wallet: {
    totalBalance: number;
    activeWallets: number;
  };
  reports: {
    pending: number;
  };
  timestamp: any;
}

export interface AdminActions {
  banUser: (userId: string, reason: string, action?: 'ban' | 'suspend', duration?: number) => Promise<void>;
  manageUser: (userId: string, action: string, data?: any) => Promise<void>;
  manageTransaction: (transactionId: string, action: string, reason?: string) => Promise<void>;
  sendBroadcastNotification: (title: string, message: string, targetRole?: string) => Promise<void>;
}

export interface UseAdminDataReturn {
  // Data
  dashboardStats: AdminDashboardStats | null;
  
  // Loading states
  isLoading: boolean;
  isRefreshing: boolean;
  
  // Error handling
  error: string | null;
  
  // Actions
  actions: AdminActions;
  
  // Utilities
  refreshStats: () => Promise<void>;
  clearError: () => void;
}

export const useAdminData = (): UseAdminDataReturn => {
  const { currentUser, isAdmin } = useAuth();
  const [dashboardStats, setDashboardStats] = useState<AdminDashboardStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch dashboard stats
  const fetchDashboardStats = useCallback(async (isRefresh = false) => {
    if (!currentUser || !isAdmin) {
      setIsLoading(false);
      return;
    }

    try {
      if (isRefresh) {
        setIsRefreshing(true);
      } else {
        setIsLoading(true);
      }
      setError(null);

      // Use Firebase Function for comprehensive stats
      const adminFetchDashboardStats = httpsCallable(functions, 'adminFetchDashboardStats');
      const result = await adminFetchDashboardStats();
      const data = result.data as { success: boolean; data: AdminDashboardStats };

      if (data.success) {
        setDashboardStats(data.data);
      } else {
        throw new Error('Failed to fetch dashboard stats');
      }

    } catch (err) {
      console.error('Error fetching dashboard stats:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch dashboard stats');
      
      // Fallback to local service
      try {
        const fallbackStats = await AdminDataService.getMetrics();
        setDashboardStats({
          users: {
            total: fallbackStats.totalUsers,
            active: fallbackStats.activeUsers,
            banned: 0,
            verified: 0,
            recentGrowth: 0
          },
          listings: {
            total: fallbackStats.totalListings,
            active: fallbackStats.activeListings,
            pending: 0,
            recentGrowth: 0
          },
          orders: {
            total: fallbackStats.totalTransactions,
            completed: 0,
            revenue: fallbackStats.totalRevenue,
            averageOrderValue: 0
          },
          wallet: {
            totalBalance: 0,
            activeWallets: 0
          },
          reports: {
            pending: fallbackStats.pendingReports
          },
          timestamp: new Date()
        });
      } catch (fallbackErr) {
        console.error('Fallback stats also failed:', fallbackErr);
      }
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  }, [currentUser, isAdmin]);

  // Admin actions
  const actions: AdminActions = {
    banUser: async (userId: string, reason: string, action = 'ban', duration?: number) => {
      try {
        const adminBanUser = httpsCallable(functions, 'adminBanUser');
        const result = await adminBanUser({ userId, reason, action, duration });
        const data = result.data as { success: boolean; message: string };
        
        if (!data.success) {
          throw new Error(data.message || 'Failed to ban user');
        }
        
        // Refresh stats after action
        await fetchDashboardStats(true);
      } catch (err) {
        console.error('Error banning user:', err);
        throw err;
      }
    },

    manageUser: async (userId: string, action: string, data?: any) => {
      try {
        const adminManageUser = httpsCallable(functions, 'adminManageUser');
        const result = await adminManageUser({ userId, action, data });
        const responseData = result.data as { success: boolean; message: string };
        
        if (!responseData.success) {
          throw new Error(responseData.message || 'Failed to manage user');
        }
        
        // Refresh stats after action
        await fetchDashboardStats(true);
      } catch (err) {
        console.error('Error managing user:', err);
        throw err;
      }
    },

    manageTransaction: async (transactionId: string, action: string, reason?: string) => {
      try {
        const adminManageTransaction = httpsCallable(functions, 'adminManageTransaction');
        const result = await adminManageTransaction({ transactionId, action, reason });
        const data = result.data as { success: boolean; message: string };
        
        if (!data.success) {
          throw new Error(data.message || 'Failed to manage transaction');
        }
        
        // Refresh stats after action
        await fetchDashboardStats(true);
      } catch (err) {
        console.error('Error managing transaction:', err);
        throw err;
      }
    },

    sendBroadcastNotification: async (title: string, message: string, targetRole?: string) => {
      try {
        await AdminDataService.sendBroadcastNotification(
          currentUser!.uid,
          title,
          message,
          targetRole as any
        );
      } catch (err) {
        console.error('Error sending broadcast notification:', err);
        throw err;
      }
    }
  };

  // Refresh stats
  const refreshStats = useCallback(async () => {
    await fetchDashboardStats(true);
  }, [fetchDashboardStats]);

  // Clear error
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Initial load
  useEffect(() => {
    fetchDashboardStats();
  }, [fetchDashboardStats]);

  // Set up real-time updates for basic metrics
  useEffect(() => {
    if (!currentUser || !isAdmin) return;

    const unsubscribe = AdminDataService.subscribeToMetrics((newMetrics) => {
      // Update only basic metrics from real-time subscription
      setDashboardStats(prev => prev ? {
        ...prev,
        users: {
          ...prev.users,
          total: newMetrics.totalUsers,
          active: newMetrics.activeUsers
        },
        listings: {
          ...prev.listings,
          total: newMetrics.totalListings,
          active: newMetrics.activeListings
        },
        reports: {
          pending: newMetrics.pendingReports
        }
      } : null);
    });

    return unsubscribe;
  }, [currentUser, isAdmin]);

  return {
    dashboardStats,
    isLoading,
    isRefreshing,
    error,
    actions,
    refreshStats,
    clearError
  };
};
