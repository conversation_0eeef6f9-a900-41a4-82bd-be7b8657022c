import React, { useState, useEffect } from 'react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import { Heart, Share, Flag, Star, ChevronLeft, ChevronRight, Eye, Clock, Edit, Trash2 } from 'lucide-react';
import BuyAndChatActions from '../components/BuyAndChatActions';
import SellerInfoCard from '../components/SellerInfoCard';
import HiveCampusLoader from '../components/HiveCampusLoader';
import { useListings } from '../hooks/useListings';
import { useAuth } from '../hooks/useAuth';
import { Listing, User } from '../firebase/types';
import { formatTimestamp } from '../utils/timestamp';
import { validateListingForCheckout } from '../utils/listingValidation';
import { doc, getDoc } from 'firebase/firestore';
import { firestore } from '../firebase/config';
import { useSuggestedListings } from '../hooks/useSuggestedListings';

const ViewListing: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { currentUser } = useAuth();
  const { fetchListing, isLoading, removeListing } = useListings();
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isLiked, setIsLiked] = useState(false);
  const [userRating, setUserRating] = useState(0);
  const [hoverRating, setHoverRating] = useState(0);
  const [hasRated, setHasRated] = useState(false);
  const [listing, setListing] = useState<Listing | null>(null);
  const [sellerProfile, setSellerProfile] = useState<User | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Get suggested listings (only if listing is loaded)
  const { suggestedListings, isLoading: suggestionsLoading } = useSuggestedListings({
    currentListing: listing,
    maxSuggestions: 6
  });
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  // Fetch seller profile data
  const fetchSellerProfile = async (ownerId: string) => {
    try {
      console.log('Fetching seller profile for ownerId:', ownerId);
      if (!ownerId) {
        console.error('No ownerId provided to fetchSellerProfile');
        return;
      }

      const userDoc = await getDoc(doc(firestore, 'users', ownerId));
      if (userDoc.exists()) {
        const userData = userDoc.data() as User;
        console.log('Seller profile fetched successfully:', userData);
        setSellerProfile(userData);
      } else {
        console.warn('Seller profile not found for ownerId:', ownerId);
      }
    } catch (err) {
      console.error('Error fetching seller profile:', err);
      // Don't throw the error, just log it - seller profile is not critical for viewing listing
    }
  };

  // Handle edit listing
  const handleEditListing = () => {
    if (listing?.id) {
      navigate(`/edit-listing/${listing.id}`);
    }
  };

  // Handle delete listing
  const handleDeleteListing = () => {
    setShowDeleteModal(true);
  };

  const confirmDeleteListing = async () => {
    if (!listing?.id) return;

    setIsDeleting(true);
    setShowDeleteModal(false);

    try {
      await removeListing(listing.id);
      navigate('/profile'); // Redirect to profile after deletion
    } catch (error) {
      console.error('Error deleting listing:', error);
      alert('Failed to delete listing. Please try again.');
    } finally {
      setIsDeleting(false);
    }
  };

  const cancelDeleteListing = () => {
    setShowDeleteModal(false);
  };

  // Fetch listing data
  useEffect(() => {
    const loadListing = async () => {
      if (!id) {
        console.error('ViewListing: No listing ID provided');
        setError('No listing ID provided');
        return;
      }

      try {
        console.log('Loading listing with ID:', id);
        const result = await fetchListing(id);

        // The fetchListing function already extracts the data from the Firebase response
        // So result should be the listing data directly, not wrapped in success/data
        if (result && typeof result === 'object') {
          console.log('Listing loaded successfully:', result);
          setListing(result);

          // Validate the listing for potential issues
          const validation = validateListingForCheckout(result);
          if (!validation.isValid) {
            console.warn('⚠️ Listing has validation issues:', validation.errors);
          }
          if (validation.warnings.length > 0) {
            console.warn('⚠️ Listing validation warnings:', validation.warnings);
          }

          // Fetch seller profile after getting listing
          if (result?.ownerId) {
            console.log('Found ownerId in listing:', result.ownerId);
            await fetchSellerProfile(result.ownerId);
          } else {
            console.warn('No ownerId found in listing data:', result);
          }
        } else {
          console.error('Invalid listing result:', result);
          setError('Listing not found');
        }
      } catch (err) {
        console.error('Error fetching listing:', err);
        setError(err instanceof Error ? err.message : 'Failed to load listing');
      }
    };

    loadListing();
  }, [id, fetchListing]);

  // Show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 md:ml-64 flex items-center justify-center overflow-x-hidden">
        <div className="text-center">
          <HiveCampusLoader size="medium" className="mx-auto mb-4" />
          <p className="text-gray-600 dark:text-gray-400">Loading listing...</p>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 md:ml-64 flex items-center justify-center overflow-x-hidden">
        <div className="text-center">
          <p className="text-red-600 dark:text-red-400 mb-4">{error}</p>
          <Link
            to="/home"
            className="text-primary-600 hover:text-primary-700 dark:text-primary-400"
          >
            Go back to home
          </Link>
        </div>
      </div>
    );
  }

  // Show not found if no listing
  if (!listing) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 md:ml-64 flex items-center justify-center overflow-x-hidden">
        <div className="text-center">
          <p className="text-gray-600 dark:text-gray-400 mb-4">Listing not found</p>
          <Link
            to="/home"
            className="text-primary-600 hover:text-primary-700 dark:text-primary-400"
          >
            Go back to home
          </Link>
        </div>
      </div>
    );
  }





  const nextImage = () => {
    setCurrentImageIndex((prev) =>
      prev === (listing.imageURLs?.length || 1) - 1 ? 0 : prev + 1
    );
  };

  const prevImage = () => {
    setCurrentImageIndex((prev) =>
      prev === 0 ? (listing.imageURLs?.length || 1) - 1 : prev - 1
    );
  };

  const handleRating = (rating: number) => {
    if (!hasRated) {
      setUserRating(rating);
      setHasRated(true);
      console.log('User rated product:', rating);
      // Here you would typically send the rating to your backend
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 md:ml-64 overflow-x-hidden">
      <div className="max-w-7xl mx-auto px-2 sm:px-4 lg:px-6 xl:px-8 py-4 sm:py-6 lg:py-8 w-full">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Image Gallery */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
              <div className="relative aspect-square rounded-2xl overflow-hidden bg-white dark:bg-gray-800 shadow-lg mb-4">
                <img
                  src={listing.imageURLs?.[currentImageIndex] || 'https://images.pexels.com/photos/699122/pexels-photo-699122.jpeg?auto=compress&cs=tinysrgb&w=800'}
                  alt={listing.title}
                  className="w-full h-full object-cover"
                />
                {(listing.imageURLs?.length || 0) > 1 && (
                  <>
                    <button
                      onClick={prevImage}
                      className="absolute left-4 top-1/2 transform -translate-y-1/2 w-10 h-10 bg-white/80 rounded-full flex items-center justify-center hover:bg-white transition-all"
                    >
                      <ChevronLeft className="w-6 h-6" />
                    </button>
                    <button
                      onClick={nextImage}
                      className="absolute right-4 top-1/2 transform -translate-y-1/2 w-10 h-10 bg-white/80 rounded-full flex items-center justify-center hover:bg-white transition-all"
                    >
                      <ChevronRight className="w-6 h-6" />
                    </button>
                  </>
                )}
                <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                  {listing.imageURLs?.map((_, index: number) => (
                    <button
                      key={index}
                      onClick={() => setCurrentImageIndex(index)}
                      className={`w-3 h-3 rounded-full transition-all ${
                        index === currentImageIndex ? 'bg-white' : 'bg-white/50'
                      }`}
                    />
                  ))}
                </div>
              </div>
              
              {/* Thumbnail Grid */}
              {(listing.imageURLs?.length || 0) > 1 && (
                <div className="grid grid-cols-3 gap-2">
                  {listing.imageURLs?.map((image, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentImageIndex(index)}
                      className={`aspect-square rounded-xl overflow-hidden ${
                        index === currentImageIndex ? 'ring-2 ring-primary-500' : ''
                      }`}
                    >
                      <img
                        src={image}
                        alt={`${listing.title} ${index + 1}`}
                        className="w-full h-full object-cover"
                    />
                  </button>
                ))}
              </div>
              )}
            </div>

            {/* Product Info */}
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
              {/* Header */}
              <div className="flex items-start justify-between mb-6">
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-2">
                    <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                      {listing.title}
                    </h1>

                    {/* Edit and Delete buttons - only show for listing owner */}
                    {currentUser && listing.ownerId === currentUser.uid && (
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={handleEditListing}
                          className="flex items-center space-x-2 px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors"
                          title="Edit listing"
                        >
                          <Edit className="w-4 h-4" />
                          <span>Edit</span>
                        </button>
                        <button
                          onClick={handleDeleteListing}
                          disabled={isDeleting}
                          className="flex items-center space-x-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors disabled:opacity-50"
                          title="Delete listing"
                        >
                          {isDeleting ? (
                            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                          ) : (
                            <Trash2 className="w-4 h-4" />
                          )}
                          <span>{isDeleting ? 'Deleting...' : 'Delete'}</span>
                        </button>
                      </div>
                    )}
                  </div>

                  <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400 mb-4">
                    <span>{listing.category}</span>
                    <span>•</span>
                    <span>{listing.university}</span>
                    <span>•</span>
                    <span>{formatTimestamp(listing.createdAt)}</span>
                  </div>
                  
                  {/* Product Rating */}
                  <div className="flex items-center space-x-4 mb-4">
                    <div className="flex items-center space-x-2">
                      <div className="flex">
                        {[1, 2, 3, 4, 5].map((star) => (
                          <Star
                            key={star}
                            className="w-5 h-5 text-gray-300 dark:text-gray-600"
                          />
                        ))}
                      </div>
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        No reviews yet
                      </span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setIsLiked(!isLiked)}
                    className={`p-2 rounded-full transition-all ${
                      isLiked 
                        ? 'bg-red-100 text-red-600' 
                        : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
                    }`}
                  >
                    <Heart className={`w-5 h-5 ${isLiked ? 'fill-current' : ''}`} />
                  </button>
                  <button className="p-2 rounded-full bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400">
                    <Share className="w-5 h-5" />
                  </button>
                  <button className="p-2 rounded-full bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400">
                    <Flag className="w-5 h-5" />
                  </button>
                </div>
              </div>

              {/* Price */}
              <div className="flex items-center space-x-4 mb-6">
                <span className="text-4xl font-bold text-primary-600 dark:text-primary-400">
                  ${listing.price}
                </span>
                <span className="bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-400 px-3 py-1 rounded-full text-sm font-medium">
                  {listing.condition}
                </span>
              </div>

              {/* Condition */}
              <div className="flex items-center space-x-2 mb-6">
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Condition:</span>
                <span className="bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-400 px-3 py-1 rounded-full text-sm font-medium">
                  {listing.condition}
                </span>
              </div>

              {/* Description */}
              <div className="mb-6">
                <h3 className="font-semibold text-gray-900 dark:text-white mb-3">Description</h3>
                <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                  {listing.description}
                </p>
              </div>

              {/* Rate This Product */}
              <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
                <h3 className="font-semibold text-gray-900 dark:text-white mb-4">Rate This Product</h3>
                <div className="flex items-center space-x-4">
                  <div className="flex space-x-1">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <button
                        key={star}
                        onClick={() => handleRating(star)}
                        onMouseEnter={() => setHoverRating(star)}
                        onMouseLeave={() => setHoverRating(0)}
                        disabled={hasRated}
                        className="transition-all transform hover:scale-110 disabled:cursor-not-allowed"
                      >
                        <Star
                          className={`w-8 h-8 ${
                            star <= (hoverRating || userRating)
                              ? 'text-yellow-400 fill-current'
                              : 'text-gray-300 dark:text-gray-600'
                          }`}
                        />
                      </button>
                    ))}
                  </div>
                  {hasRated ? (
                    <span className="text-sm text-success-600 dark:text-success-400 font-medium">
                      Thank you for rating!
                    </span>
                  ) : (
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                      Click to rate this product
                    </span>
                  )}
                </div>
              </div>

              {/* Stats */}
              <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                <div className="flex items-center space-x-1">
                  <Eye className="w-4 h-4" />
                  <span>0 views</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Heart className="w-4 h-4" />
                  <span>0 likes</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Clock className="w-4 h-4" />
                  <span>Listed {formatTimestamp(listing.createdAt)}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Buy and Chat Actions */}
            <BuyAndChatActions
              listing={listing}
              sellerId={listing.ownerId}
              available={listing.status === 'active'}
            />

            {/* Seller Info */}
            <SellerInfoCard seller={{
              id: listing.ownerId,
              name: sellerProfile?.name || listing.ownerName || 'Anonymous',
              avatar: sellerProfile?.profilePictureURL || '/placeholder-avatar.jpg',
              rating: 4.9, // TODO: Calculate from real reviews
              reviewCount: 0, // TODO: Count real reviews
              verified: true,
              university: sellerProfile?.university || listing.university,
              totalSales: 0, // TODO: Count real sales
              responseRate: 98, // TODO: Calculate real response rate
              responseTime: '< 1 hour' // TODO: Calculate real response time
            }} />


          </div>
        </div>

        {/* Suggested Listings Section */}
        {listing && suggestedListings.length > 0 && (
          <div className="mt-12">
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                  You might also like
                </h2>
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  AI-powered suggestions
                </span>
              </div>

              {suggestionsLoading ? (
                <>
                  {/* Mobile loading state: Horizontal scroll */}
                  <div className="sm:hidden">
                    <div className="flex gap-4 overflow-x-auto pb-4 scrollbar-hide">
                      {[...Array(6)].map((_, index) => (
                        <div key={index} className="animate-pulse flex-shrink-0 w-48">
                          <div className="bg-gray-200 dark:bg-gray-700 rounded-xl h-48 mb-3"></div>
                          <div className="bg-gray-200 dark:bg-gray-700 rounded h-4 mb-2"></div>
                          <div className="bg-gray-200 dark:bg-gray-700 rounded h-4 w-3/4"></div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Desktop loading state: Grid */}
                  <div className="hidden sm:grid sm:grid-cols-2 lg:grid-cols-3 gap-4">
                    {[...Array(6)].map((_, index) => (
                      <div key={index} className="animate-pulse">
                        <div className="bg-gray-200 dark:bg-gray-700 rounded-xl h-48 mb-3"></div>
                        <div className="bg-gray-200 dark:bg-gray-700 rounded h-4 mb-2"></div>
                        <div className="bg-gray-200 dark:bg-gray-700 rounded h-4 w-3/4"></div>
                      </div>
                    ))}
                  </div>
                </>
              ) : (
                <>
                  {/* Mobile: Horizontal scroll */}
                  <div className="sm:hidden">
                    <div className="flex gap-4 overflow-x-auto pb-4 scrollbar-hide">
                      {suggestedListings.map((suggestedListing) => (
                        <Link
                          key={suggestedListing.id}
                          to={`/listing/${suggestedListing.id}`}
                          className="group bg-gray-50 dark:bg-gray-700 rounded-xl overflow-hidden hover:shadow-lg transition-all duration-300 hover:scale-[1.02] flex-shrink-0 w-48"
                        >
                          <div className="aspect-square bg-gray-100 dark:bg-gray-600 overflow-hidden">
                            <img
                              src={suggestedListing.imageURLs?.[0] || '/placeholder-image.jpg'}
                              alt={suggestedListing.title}
                              className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                            />
                          </div>
                          <div className="p-4">
                            <h3 className="font-semibold text-gray-900 dark:text-white text-sm mb-1 line-clamp-2">
                              {suggestedListing.title}
                            </h3>
                            <p className="text-lg font-bold text-primary-600 dark:text-primary-400 mb-1">
                              ${suggestedListing.price.toFixed(2)}
                            </p>
                            <p className="text-xs text-gray-500 dark:text-gray-400 capitalize">
                              {suggestedListing.condition.replace('_', ' ')} • {suggestedListing.category}
                            </p>
                          </div>
                        </Link>
                      ))}
                    </div>
                  </div>

                  {/* Desktop: Grid layout */}
                  <div className="hidden sm:grid sm:grid-cols-2 lg:grid-cols-3 gap-4">
                    {suggestedListings.map((suggestedListing) => (
                      <Link
                        key={suggestedListing.id}
                        to={`/listing/${suggestedListing.id}`}
                        className="group bg-gray-50 dark:bg-gray-700 rounded-xl overflow-hidden hover:shadow-lg transition-all duration-300 hover:scale-[1.02]"
                      >
                        <div className="aspect-square bg-gray-100 dark:bg-gray-600 overflow-hidden">
                          <img
                            src={suggestedListing.imageURLs?.[0] || '/placeholder-image.jpg'}
                            alt={suggestedListing.title}
                            className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                          />
                        </div>
                        <div className="p-4">
                          <h3 className="font-semibold text-gray-900 dark:text-white text-sm mb-1 line-clamp-2">
                            {suggestedListing.title}
                          </h3>
                          <p className="text-lg font-bold text-primary-600 dark:text-primary-400 mb-1">
                            ${suggestedListing.price.toFixed(2)}
                          </p>
                          <p className="text-xs text-gray-500 dark:text-gray-400 capitalize">
                            {suggestedListing.condition.replace('_', ' ')} • {suggestedListing.category}
                          </p>
                        </div>
                      </Link>
                    ))}
                  </div>
                </>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl max-w-md w-full p-6">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mr-4">
                <Trash2 className="w-6 h-6 text-red-600 dark:text-red-400" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Delete Listing
                </h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  This action cannot be undone
                </p>
              </div>
            </div>

            <p className="text-gray-700 dark:text-gray-300 mb-6">
              Are you sure you want to permanently delete this listing? This will remove it from your profile and it cannot be recovered.
            </p>

            <div className="flex space-x-3">
              <button
                onClick={cancelDeleteListing}
                className="flex-1 px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
              >
                Don't Delete
              </button>
              <button
                onClick={confirmDeleteListing}
                disabled={isDeleting}
                className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50"
              >
                {isDeleting ? 'Deleting...' : 'Delete'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ViewListing;