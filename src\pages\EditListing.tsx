import React, { useState, useEffect } from 'react';
import { Upload, DollarSign, Trash2, Save, ArrowLeft, X } from 'lucide-react';
import { useParams, useNavigate } from 'react-router-dom';
import toast from 'react-hot-toast';
import { useAuth } from '../hooks/useAuth';
import { editListing, deleteListing, getListingById } from '../firebase/listings';
import { uploadFile } from '../firebase/uploads';
import { ListingCondition, ListingType, Listing } from '../firebase/types';
import { isRiskyMessage, getRiskType, logViolation } from '../utils/detectRisk';
import { roundPrice } from '../utils/priceUtils';
import { categoryNames, getBackendCategoryValue, getDisplayCategoryName } from '../constants/categories';

// Interface for listing update data
interface ListingUpdateData {
  listingId: string;
  title: string;
  description: string;
  price: number;
  category: string;
  condition: ListingCondition;
  imageURLs: string[];
  // Rent-specific fields
  rentalPeriod?: 'weekly' | 'monthly';
  weeklyPrice?: number;
  monthlyPrice?: number;
  startDate?: string;
  endDate?: string;
  // Auction-specific fields
  startingBid?: number;
  auctionStartDate?: string;
  auctionStartTime?: string;
  auctionEndDate?: string;
  auctionEndTime?: string;
  auctionDuration?: number;
}

const EditListing: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { currentUser } = useAuth();

  const [listing, setListing] = useState<Listing | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    price: '',
    category: '',
    condition: '' as ListingCondition,
    type: 'sell' as ListingType,
    images: [] as (string | File)[],
    visibility: 'university' as 'university' | 'public',
    // Rent-specific fields
    rentalPeriod: 'weekly' as 'weekly' | 'monthly',
    weeklyPrice: '',
    monthlyPrice: '',
    startDate: '',
    endDate: '',
    // Auction-specific fields
    startingBid: '',
    auctionStartDate: '',
    auctionStartTime: '',
    auctionEndDate: '',
    auctionEndTime: '',
    auctionDuration: '7' as '1' | '3' | '7' | '14'
  });

  const categories = categoryNames;

  // Map UI-friendly condition names to backend values
  const conditionMap: Record<string, ListingCondition> = {
    'Brand New': 'new',
    'Like New': 'like_new',
    'Very Good': 'very_good',
    'Good': 'good',
    'Fair': 'fair',
    'Poor': 'poor'
  };

  const conditionOptions = Object.keys(conditionMap);

  // Load listing data on component mount
  useEffect(() => {
    const loadListing = async () => {
      if (!id) {
        setError('No listing ID provided');
        setIsLoading(false);
        return;
      }

      try {
        const result = await getListingById(id) as { success: boolean; data: Listing };
        if (result.success && result.data) {
          const listingData = result.data;
          setListing(listingData);

          // Check if current user owns this listing
          if (listingData.ownerId !== currentUser?.uid) {
            setError('You can only edit your own listings');
            setIsLoading(false);
            return;
          }

          // Populate form with existing data
          setFormData({
            title: listingData.title,
            description: listingData.description,
            price: listingData.price.toString(),
            category: getDisplayCategoryName(listingData.category),
            condition: listingData.condition,
            type: listingData.type,
            images: listingData.imageURLs || [],
            visibility: listingData.visibility || 'university',
            rentalPeriod: listingData.rentalPeriod || 'weekly',
            weeklyPrice: listingData.weeklyPrice?.toString() || '',
            monthlyPrice: listingData.monthlyPrice?.toString() || '',
            startDate: listingData.startDate || '',
            endDate: listingData.endDate || '',
            startingBid: listingData.startingBid?.toString() || '',
            auctionStartDate: listingData.auctionStartDate || '',
            auctionStartTime: listingData.auctionStartTime || '',
            auctionEndDate: listingData.auctionEndDate || '',
            auctionEndTime: listingData.auctionEndTime || '',
            auctionDuration: (listingData.auctionDuration?.toString() as '1' | '3' | '7' | '14') || '7'
          });
        } else {
          setError('Listing not found');
        }
      } catch (err) {
        console.error('Error loading listing:', err);
        setError('Failed to load listing');
      } finally {
        setIsLoading(false);
      }
    };

    loadListing();
  }, [id, currentUser]);

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const files = Array.from(e.target.files);
      setFormData({ ...formData, images: [...formData.images, ...files] });
    }
  };

  const removeImage = (index: number) => {
    const newImages = formData.images.filter((_, i) => i !== index);
    setFormData({ ...formData, images: newImages });
  };

  const handleSave = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!id || !listing) return;

    // Check for risky content in title and description
    if (isRiskyMessage(formData.title) || isRiskyMessage(formData.description)) {
      const titleRisk = isRiskyMessage(formData.title) ? getRiskType(formData.title) : null;
      const descriptionRisk = isRiskyMessage(formData.description) ? getRiskType(formData.description) : null;
      const riskType = titleRisk || descriptionRisk;

      const errorMessage = '⚠️ Personal payment details and contact info are not allowed in listings.';
      toast.error(errorMessage);

      // Log the violation
      await logViolation('listing',
        `Title: ${formData.title}\nDescription: ${formData.description}`,
        riskType,
        { listingId: id, listingTitle: formData.title }
      );

      return; // Don't save the listing
    }

    setIsSaving(true);
    setError(null);

    try {
      // Upload new images if any
      const newImages = formData.images.filter(img => img instanceof File) as File[];
      const uploadedImageUrls: string[] = [];

      if (newImages.length > 0) {
        for (const file of newImages) {
          const result = await uploadFile(file, 'listing');
          uploadedImageUrls.push(result.downloadURL);
        }
      }

      // Combine existing image URLs with new ones
      const existingImageUrls = formData.images.filter(img => typeof img === 'string') as string[];
      const allImageUrls = [...existingImageUrls, ...uploadedImageUrls];

      // Prepare update data
      const updateData: ListingUpdateData = {
        listingId: id,
        title: formData.title,
        description: formData.description,
        price: roundPrice(parseFloat(formData.price)),
        category: getBackendCategoryValue(formData.category),
        condition: formData.condition,
        imageURLs: allImageUrls
      };

      // Add type-specific fields
      if (formData.type === 'rent') {
        updateData.rentalPeriod = formData.rentalPeriod;
        if (formData.weeklyPrice) updateData.weeklyPrice = roundPrice(parseFloat(formData.weeklyPrice));
        if (formData.monthlyPrice) updateData.monthlyPrice = roundPrice(parseFloat(formData.monthlyPrice));
        if (formData.startDate) updateData.startDate = formData.startDate;
        if (formData.endDate) updateData.endDate = formData.endDate;
      } else if (formData.type === 'auction') {
        if (formData.startingBid) updateData.startingBid = roundPrice(parseFloat(formData.startingBid));
        if (formData.auctionStartDate) updateData.auctionStartDate = formData.auctionStartDate;
        if (formData.auctionStartTime) updateData.auctionStartTime = formData.auctionStartTime;
        if (formData.auctionEndDate) updateData.auctionEndDate = formData.auctionEndDate;
        if (formData.auctionEndTime) updateData.auctionEndTime = formData.auctionEndTime;
        if (formData.auctionDuration) updateData.auctionDuration = parseInt(formData.auctionDuration);
      }

      const result = await editListing(updateData);

      if ((result as { success: boolean }).success) {
        navigate(`/listing/${id}`);
      } else {
        setError('Failed to update listing');
      }
    } catch (err) {
      console.error('Error updating listing:', err);
      let errorMessage = 'Failed to update listing. Please try again.';

      if (err instanceof Error) {
        // Handle specific Firebase errors
        if (err.message.includes('CORS')) {
          errorMessage = 'Network error. Please try again in a moment.';
        } else if (err.message.includes('permission-denied')) {
          errorMessage = 'You do not have permission to edit this listing.';
        } else if (err.message.includes('not-found')) {
          errorMessage = 'Listing not found.';
        } else if (err.message.includes('internal')) {
          errorMessage = 'Server error. Please try again later.';
        }
      }

      setError(errorMessage);
    } finally {
      setIsSaving(false);
    }
  };

  const handleDelete = async () => {
    if (!id || !listing) return;

    if (!confirm('Are you sure you want to delete this listing? This action cannot be undone.')) {
      return;
    }

    setIsDeleting(true);
    setError(null);

    try {
      const result = await deleteListing(id);

      if ((result as any).success) {
        navigate('/profile');
      } else {
        setError('Failed to delete listing');
      }
    } catch (err) {
      console.error('Error deleting listing:', err);
      setError('Failed to delete listing. Please try again.');
    } finally {
      setIsDeleting(false);
    }
  };

  const handleToggleFree = () => {
    if (formData.type === 'sell') {
      setFormData({
        ...formData,
        price: formData.price === '0' ? '' : '0'
      });
    }
  };

  const getImageUrl = (image: string | File) => {
    return typeof image === 'string' ? image : URL.createObjectURL(image);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 md:ml-64 flex items-center justify-center">
        <div className="animate-pulse flex flex-col items-center">
          <div className="w-16 h-16 bg-primary-200 dark:bg-primary-700 rounded-full mb-4"></div>
          <div className="h-4 w-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 md:ml-64 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">Error</h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
          <button
            onClick={() => navigate('/profile')}
            className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg transition-colors"
          >
            Go Back to Profile
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 md:ml-64">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-4 mb-4">
            <button
              onClick={() => navigate(-1)}
              className="flex items-center space-x-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
              <span>Back</span>
            </button>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Edit Listing</h1>
          <p className="text-gray-600 dark:text-gray-400">Update your listing details</p>
        </div>

        {/* Form */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8">
          <form onSubmit={handleSave} className="space-y-8">
            {/* Image Upload */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">
                Photos (up to 8)
              </label>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {formData.images.map((image, index) => (
                  <div key={index} className="relative aspect-square rounded-xl overflow-hidden bg-gray-100 dark:bg-gray-700">
                    <img
                      src={getImageUrl(image)}
                      alt={`Upload ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                    <button
                      type="button"
                      onClick={() => removeImage(index)}
                      className="absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full hover:bg-red-600 transition-colors"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                ))}
                {formData.images.length < 8 && (
                  <label className="aspect-square border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-xl flex flex-col items-center justify-center cursor-pointer hover:border-primary-500 transition-colors">
                    <Upload className="w-8 h-8 text-gray-400 mb-2" />
                    <span className="text-sm text-gray-500 dark:text-gray-400">Add Photo</span>
                    <input
                      type="file"
                      multiple
                      accept="image/*"
                      onChange={handleImageUpload}
                      className="hidden"
                    />
                  </label>
                )}
              </div>
            </div>

            {/* Title */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Title
              </label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                placeholder="What are you selling?"
                required
              />
            </div>

            {/* Category & Condition */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Category
                </label>
                <select
                  value={formData.category}
                  onChange={(e) => setFormData({ ...formData, category: e.target.value })}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  required
                >
                  {categories.map((category) => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Condition
                </label>
                <select
                  value={Object.keys(conditionMap).find(key => conditionMap[key] === formData.condition) || ''}
                  onChange={(e) => setFormData({ ...formData, condition: conditionMap[e.target.value] })}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  required
                >
                  <option value="">Select condition</option>
                  {conditionOptions.map((condition) => (
                    <option key={condition} value={condition}>{condition}</option>
                  ))}
                </select>
              </div>
            </div>

            {/* Price */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Price
                </label>
                {formData.type === 'sell' && (
                  <button
                    type="button"
                    onClick={handleToggleFree}
                    className={`text-sm px-3 py-1 rounded-full transition-colors ${
                      formData.price === '0'
                        ? 'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400'
                        : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                    }`}
                  >
                    {formData.price === '0' ? 'Free Item ✓' : 'Mark as Free'}
                  </button>
                )}
              </div>
              <div className="relative">
                <DollarSign className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                <input
                  type="number"
                  value={formData.price}
                  onChange={(e) => setFormData({ ...formData, price: e.target.value })}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  placeholder="0.00"
                  step="0.01"
                  min="0"
                  required
                />
              </div>
            </div>

            {/* Description */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Description
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                rows={4}
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white resize-none"
                placeholder="Describe your item in detail..."
                required
              />

              {/* Safety Warning Banner */}
              <div className="mt-2 p-3 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg">
                <div className="flex items-start text-sm text-amber-700 dark:text-amber-300">
                  <div className="flex-shrink-0 mr-2 mt-0.5">
                    📢
                  </div>
                  <span>
                    Please avoid using phone numbers, emails, or external payment links. Keep all communication and transactions within Hive Campus for your safety.
                  </span>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex space-x-4">
              <button
                type="button"
                onClick={handleDelete}
                disabled={isDeleting}
                className="flex-1 py-3 px-6 border border-red-300 text-red-600 rounded-xl font-semibold hover:bg-red-50 transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
              >
                {isDeleting ? (
                  <>
                    <div className="w-4 h-4 border-2 border-red-600 border-t-transparent rounded-full animate-spin" />
                    <span>Deleting...</span>
                  </>
                ) : (
                  <>
                    <Trash2 className="w-4 h-4" />
                    <span>Delete Listing</span>
                  </>
                )}
              </button>
              <button
                type="button"
                onClick={() => navigate(-1)}
                className="flex-1 py-3 px-6 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-xl font-semibold hover:bg-gray-50 dark:hover:bg-gray-700 transition-all flex items-center justify-center space-x-2"
              >
                <X className="w-4 h-4" />
                <span>Cancel</span>
              </button>
              <button
                type="submit"
                disabled={isSaving}
                className="flex-1 bg-gradient-to-r from-primary-600 to-primary-700 text-white py-3 px-6 rounded-xl font-semibold hover:from-primary-700 hover:to-primary-800 transform hover:scale-[1.02] transition-all duration-200 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
              >
                {isSaving ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    <span>Saving...</span>
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4" />
                    <span>Update Listing</span>
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default EditListing;