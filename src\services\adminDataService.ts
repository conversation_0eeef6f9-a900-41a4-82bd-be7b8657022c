import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  updateDoc,
  deleteDoc,
  onSnapshot,
  Timestamp,
  QueryDocumentSnapshot,
  DocumentData,
  writeBatch
} from 'firebase/firestore';
import { httpsCallable } from 'firebase/functions';
import { firestore, functions } from '../firebase/config';
import { User, Listing } from '../firebase/types';

export interface AdminMetrics {
  totalUsers: number;
  activeUsers: number;
  totalListings: number;
  activeListings: number;
  totalTransactions: number;
  totalRevenue: number;
  pendingReports: number;
  pendingIssues: number;
}

export interface PaginationOptions {
  pageSize?: number;
  lastDoc?: QueryDocumentSnapshot<DocumentData>;
}

export interface FilterOptions {
  role?: string;
  university?: string;
  status?: string;
  category?: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
}

/**
 * Admin Data Service for Firebase operations
 */
export class AdminDataService {
  
  /**
   * Get platform metrics for admin dashboard
   */
  static async getMetrics(): Promise<AdminMetrics> {
    try {
      const [
        usersSnapshot,
        listingsSnapshot,
        reportsSnapshot,
        issuesSnapshot
      ] = await Promise.all([
        getDocs(collection(firestore, 'users')),
        getDocs(collection(firestore, 'listings')),
        getDocs(query(collection(firestore, 'reports'), where('status', '==', 'pending'))),
        getDocs(query(collection(firestore, 'issues'), where('status', '==', 'open')))
      ]);

      const users = usersSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })) as User[];
      const listings = listingsSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })) as Listing[];

      // Calculate active users (logged in within last 30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      const activeUsers = users.filter(user => {
        const lastLogin = user.updatedAt?.toDate() || user.createdAt.toDate();
        return lastLogin > thirtyDaysAgo;
      }).length;

      const activeListings = listings.filter(listing => listing.status === 'active').length;

      // TODO: Get real transaction data from orders collection
      const totalTransactions = 0;
      const totalRevenue = 0;

      return {
        totalUsers: users.length,
        activeUsers,
        totalListings: listings.length,
        activeListings,
        totalTransactions,
        totalRevenue,
        pendingReports: reportsSnapshot.size,
        pendingIssues: issuesSnapshot.size
      };
    } catch (error: any) {
      console.error('Error fetching admin metrics:', error);

      // Provide better error messages for common issues
      if (error.code === 'permission-denied') {
        console.warn('Admin permissions not properly set. Please check Firebase security rules and user role.');
        // Return default metrics instead of throwing
        return {
          totalUsers: 0,
          activeUsers: 0,
          totalListings: 0,
          activeListings: 0,
          totalTransactions: 0,
          totalRevenue: 0,
          pendingReports: 0,
          pendingIssues: 0
        };
      }

      throw error;
    }
  }

  /**
   * Get users with pagination and filtering
   */
  static async getUsers(
    options: PaginationOptions = {},
    filters: FilterOptions = {}
  ): Promise<{ users: User[]; hasMore: boolean; lastDoc?: QueryDocumentSnapshot<DocumentData> }> {
    try {
      const { pageSize = 20, lastDoc } = options;
      let q = query(collection(firestore, 'users'), orderBy('createdAt', 'desc'));

      // Apply filters
      if (filters.role) {
        q = query(q, where('role', '==', filters.role));
      }
      if (filters.university) {
        q = query(q, where('university', '==', filters.university));
      }
      if (filters.dateRange) {
        q = query(
          q,
          where('createdAt', '>=', Timestamp.fromDate(filters.dateRange.start)),
          where('createdAt', '<=', Timestamp.fromDate(filters.dateRange.end))
        );
      }

      // Apply pagination
      if (lastDoc) {
        q = query(q, startAfter(lastDoc));
      }
      q = query(q, limit(pageSize + 1));

      const snapshot = await getDocs(q);
      const users = snapshot.docs.slice(0, pageSize).map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as User[];

      const hasMore = snapshot.docs.length > pageSize;
      const newLastDoc = hasMore ? snapshot.docs[pageSize - 1] : undefined;

      return { users, hasMore, lastDoc: newLastDoc };
    } catch (error) {
      console.error('Error fetching users:', error);
      throw error;
    }
  }

  /**
   * Get listings with pagination and filtering
   */
  static async getListings(
    options: PaginationOptions = {},
    filters: FilterOptions = {}
  ): Promise<{ listings: Listing[]; hasMore: boolean; lastDoc?: QueryDocumentSnapshot<DocumentData> }> {
    try {
      const { pageSize = 20, lastDoc } = options;
      let q = query(collection(firestore, 'listings'), orderBy('createdAt', 'desc'));

      // Apply filters
      if (filters.status) {
        q = query(q, where('status', '==', filters.status));
      }
      if (filters.category) {
        q = query(q, where('category', '==', filters.category));
      }
      if (filters.university) {
        q = query(q, where('university', '==', filters.university));
      }

      // Apply pagination
      if (lastDoc) {
        q = query(q, startAfter(lastDoc));
      }
      q = query(q, limit(pageSize + 1));

      const snapshot = await getDocs(q);
      const listings = snapshot.docs.slice(0, pageSize).map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Listing[];

      const hasMore = snapshot.docs.length > pageSize;
      const newLastDoc = hasMore ? snapshot.docs[pageSize - 1] : undefined;

      return { listings, hasMore, lastDoc: newLastDoc };
    } catch (error) {
      console.error('Error fetching listings:', error);
      throw error;
    }
  }

  /**
   * Get universities with user and listing counts
   */
  static async getUniversities(): Promise<Array<{
    name: string;
    userCount: number;
    listingCount: number;
    domain?: string;
  }>> {
    try {
      const [usersSnapshot, listingsSnapshot] = await Promise.all([
        getDocs(collection(firestore, 'users')),
        getDocs(collection(firestore, 'listings'))
      ]);

      const users = usersSnapshot.docs.map(doc => doc.data()) as User[];
      const listings = listingsSnapshot.docs.map(doc => doc.data()) as Listing[];

      // Group by university
      const universityMap = new Map<string, { userCount: number; listingCount: number }>();

      users.forEach(user => {
        const uni = user.university;
        if (!universityMap.has(uni)) {
          universityMap.set(uni, { userCount: 0, listingCount: 0 });
        }
        universityMap.get(uni)!.userCount++;
      });

      listings.forEach(listing => {
        const uni = listing.university;
        if (!universityMap.has(uni)) {
          universityMap.set(uni, { userCount: 0, listingCount: 0 });
        }
        universityMap.get(uni)!.listingCount++;
      });

      return Array.from(universityMap.entries()).map(([name, counts]) => ({
        name,
        ...counts
      }));
    } catch (error) {
      console.error('Error fetching universities:', error);
      throw error;
    }
  }

  /**
   * Update user status or role
   */
  static async updateUser(userId: string, updates: Partial<User>): Promise<void> {
    try {
      const userRef = doc(firestore, 'users', userId);
      await updateDoc(userRef, {
        ...updates,
        updatedAt: Timestamp.now()
      });
    } catch (error) {
      console.error('Error updating user:', error);
      throw error;
    }
  }

  /**
   * Send admin warning or message to user
   */
  static async sendAdminMessage(
    userId: string,
    adminId: string,
    message: string,
    type: 'warning' | 'message' = 'message'
  ): Promise<void> {
    try {
      // Get admin and user data
      const adminDoc = await getDoc(doc(firestore, 'users', adminId));
      const userDoc = await getDoc(doc(firestore, 'users', userId));

      if (!adminDoc.exists() || !userDoc.exists()) {
        throw new Error('Admin or user not found');
      }

      const adminData = adminDoc.data();
      const userData = userDoc.data();

      // Create notification in user's notifications subcollection
      const notificationRef = collection(firestore, `users/${userId}/notifications`);
      await addDoc(notificationRef, {
        type: type === 'warning' ? 'admin_warning' : 'admin_message',
        senderId: adminId,
        senderName: adminData.name || 'Admin',
        title: type === 'warning' ? '⚠️ Warning from Admin' : '📢 Message from Admin',
        message,
        actionRequired: type === 'warning',
        expiresAt: Timestamp.fromDate(new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)), // 7 days
        createdAt: Timestamp.now(),
        dismissed: false,
        read: false,
        icon: type === 'warning' ? '⚠️' : '📢',
        priority: type === 'warning' ? 'high' : 'normal'
      });

      // Update user warning count if it's a warning
      if (type === 'warning') {
        await updateDoc(doc(firestore, 'users', userId), {
          warningCount: (userData.warningCount || 0) + 1,
          lastWarningAt: Timestamp.now(),
          updatedAt: Timestamp.now()
        });
      }

      console.log(`Admin ${type} sent to user ${userData.name || userId}`);
    } catch (error) {
      console.error('Error sending admin message:', error);
      throw error;
    }
  }

  /**
   * Send broadcast notification to all users using Firebase Function
   */
  static async sendBroadcastNotification(
    adminId: string,
    title: string,
    message: string,
    targetRole?: 'student' | 'merchant' | 'all'
  ): Promise<void> {
    try {
      console.log('Calling broadcast notification function...', { adminId, title, targetRole });

      // Use Firebase Function for reliable server-side processing
      const sendBroadcastFunction = httpsCallable(functions, 'sendBroadcastNotification');

      const result = await sendBroadcastFunction({
        title,
        message,
        targetRole
      });

      console.log('Broadcast notification result:', result.data);

      if (!result.data?.success) {
        throw new Error(result.data?.message || 'Failed to send broadcast notification');
      }

    } catch (error: any) {
      console.error('Error sending broadcast notification:', error);

      // If Firebase Function fails, fall back to direct Firestore writes
      if (error?.code === 'functions/not-found' || error?.code === 'functions/unavailable') {
        console.log('Firebase Function unavailable, falling back to direct Firestore writes...');
        return this.sendBroadcastNotificationDirect(adminId, title, message, targetRole);
      }

      throw error;
    }
  }

  /**
   * Fallback method: Send broadcast notification directly to Firestore
   */
  private static async sendBroadcastNotificationDirect(
    adminId: string,
    title: string,
    message: string,
    targetRole?: 'student' | 'merchant' | 'all'
  ): Promise<void> {
    try {
      console.log('Starting direct broadcast notification...', { adminId, title, targetRole });

      // Get admin data
      const adminDoc = await getDoc(doc(firestore, 'users', adminId));
      if (!adminDoc.exists()) {
        throw new Error('Admin not found');
      }

      const adminData = adminDoc.data();
      console.log('Admin data retrieved:', { name: adminData.name, role: adminData.role });

      // Get all users based on target role
      let usersQuery = query(collection(firestore, 'users'));
      if (targetRole && targetRole !== 'all') {
        usersQuery = query(collection(firestore, 'users'), where('role', '==', targetRole));
      }

      const usersSnapshot = await getDocs(usersQuery);
      console.log(`Found ${usersSnapshot.size} users to notify`);

      // Use multiple smaller batches to avoid batch size limits
      const batchSize = 500; // Firestore batch limit is 500 operations
      const batches: WriteBatch[] = [];
      let currentBatch = writeBatch(firestore);
      let operationCount = 0;

      // Create notifications for all users in their individual subcollections
      usersSnapshot.forEach((userDoc) => {
        const userData = userDoc.data();
        if (userData.role !== 'admin') { // Don't send to other admins
          // Create notification in user's notifications subcollection
          const notificationRef = doc(collection(firestore, `users/${userDoc.id}/notifications`));
          currentBatch.set(notificationRef, {
            type: 'admin_broadcast',
            senderId: adminId,
            senderName: adminData.name || 'Admin',
            title: `📢 ${title}`,
            message,
            actionRequired: false,
            expiresAt: Timestamp.fromDate(new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)), // 7 days
            createdAt: Timestamp.now(),
            dismissed: false,
            read: false,
            icon: '📢',
            priority: 'normal'
          });

          operationCount++;

          // If we've reached the batch size limit, start a new batch
          if (operationCount >= batchSize) {
            batches.push(currentBatch);
            currentBatch = writeBatch(firestore);
            operationCount = 0;
          }
        }
      });

      // Add the last batch if it has operations
      if (operationCount > 0) {
        batches.push(currentBatch);
      }

      console.log(`Committing ${batches.length} batches...`);

      // Commit all batches
      const batchPromises = batches.map((batch, index) => {
        console.log(`Committing batch ${index + 1}/${batches.length}`);
        return batch.commit();
      });

      await Promise.all(batchPromises);
      console.log('Direct broadcast notification sent successfully');
    } catch (error) {
      console.error('Error sending direct broadcast notification:', error);
      throw error;
    }
  }

  /**
   * Update order status and properties
   */
  static async updateOrder(orderId: string, updates: any): Promise<void> {
    try {
      const orderRef = doc(firestore, 'orders', orderId);
      await updateDoc(orderRef, {
        ...updates,
        updatedAt: Timestamp.now()
      });
    } catch (error) {
      console.error('Error updating order:', error);
      throw error;
    }
  }

  /**
   * Delete user and associated data
   */
  static async deleteUser(userId: string): Promise<void> {
    try {
      const batch = writeBatch(firestore);

      // Delete user document
      batch.delete(doc(firestore, 'users', userId));

      // Delete user's listings
      const userListings = await getDocs(
        query(collection(firestore, 'listings'), where('ownerId', '==', userId))
      );
      userListings.docs.forEach(doc => {
        batch.delete(doc.ref);
      });

      await batch.commit();
    } catch (error) {
      console.error('Error deleting user:', error);
      throw error;
    }
  }

  /**
   * Update listing status
   */
  static async updateListing(listingId: string, updates: Partial<Listing>): Promise<void> {
    try {
      const listingRef = doc(firestore, 'listings', listingId);
      await updateDoc(listingRef, {
        ...updates,
        updatedAt: Timestamp.now()
      });
    } catch (error) {
      console.error('Error updating listing:', error);
      throw error;
    }
  }

  /**
   * Delete listing
   */
  static async deleteListing(listingId: string): Promise<void> {
    try {
      await deleteDoc(doc(firestore, 'listings', listingId));
    } catch (error) {
      console.error('Error deleting listing:', error);
      throw error;
    }
  }

  /**
   * Get user's orders for admin view
   */
  static async getUserOrders(userId: string): Promise<any[]> {
    try {
      const ordersQuery = query(
        collection(firestore, 'orders'),
        where('buyerId', '==', userId)
      );
      const sellerOrdersQuery = query(
        collection(firestore, 'orders'),
        where('sellerId', '==', userId)
      );

      const [buyerOrders, sellerOrders] = await Promise.all([
        getDocs(ordersQuery),
        getDocs(sellerOrdersQuery)
      ]);

      const allOrders = [
        ...buyerOrders.docs.map(doc => ({ id: doc.id, ...doc.data(), type: 'purchase' })),
        ...sellerOrders.docs.map(doc => ({ id: doc.id, ...doc.data(), type: 'sale' }))
      ];

      return allOrders.sort((a, b) => {
        const aDate = a.createdAt?.toDate() || new Date(0);
        const bDate = b.createdAt?.toDate() || new Date(0);
        return bDate.getTime() - aDate.getTime();
      });
    } catch (error) {
      console.error('Error fetching user orders:', error);
      return [];
    }
  }

  /**
   * Get user's listings for admin view
   */
  static async getUserListings(userId: string): Promise<any[]> {
    try {
      const listingsQuery = query(
        collection(firestore, 'listings'),
        where('sellerId', '==', userId),
        orderBy('createdAt', 'desc')
      );

      const snapshot = await getDocs(listingsQuery);
      return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      console.error('Error fetching user listings:', error);
      return [];
    }
  }

  /**
   * Get transactions with filtering options
   */
  static async getTransactions(options: {
    status?: string;
    pageSize?: number;
    lastDoc?: QueryDocumentSnapshot<DocumentData>;
  } = {}): Promise<any[]> {
    try {
      const { status, pageSize = 50, lastDoc } = options;
      let q = query(
        collection(firestore, 'orders'),
        orderBy('createdAt', 'desc'),
        limit(pageSize)
      );

      if (status) {
        q = query(q, where('status', '==', status));
      }

      if (lastDoc) {
        q = query(q, startAfter(lastDoc));
      }

      const snapshot = await getDocs(q);
      return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
    } catch (error) {
      console.error('Error fetching transactions:', error);
      return [];
    }
  }

  /**
   * Subscribe to real-time metrics updates
   */
  static subscribeToMetrics(callback: (metrics: AdminMetrics) => void): () => void {
    const unsubscribes: (() => void)[] = [];

    // Subscribe to users collection
    unsubscribes.push(
      onSnapshot(collection(firestore, 'users'), () => {
        this.getMetrics().then(callback).catch(console.error);
      })
    );

    // Subscribe to listings collection
    unsubscribes.push(
      onSnapshot(collection(firestore, 'listings'), () => {
        this.getMetrics().then(callback).catch(console.error);
      })
    );

    // Return cleanup function
    return () => {
      unsubscribes.forEach(unsubscribe => unsubscribe());
    };
  }
}
