// Console error filtering utility to reduce noise from third-party services

interface ConsoleMethod {
  (...args: any[]): void;
}

// Store original console methods
const originalConsole = {
  error: console.error,
  warn: console.warn,
  log: console.log
};

// Known patterns of errors/warnings that are safe to filter
const FILTERED_PATTERNS = [
  // Stripe-related errors that don't affect functionality
  /Cannot find module.*\/en/,
  /preload.*uses an unsupported.*as.*value/,
  /Too Many Requests.*stripe\.com/,
  /Failed to load resource.*stripe\.com.*429/,
  
  // Other third-party service noise
  /sentry.*429/,
  /reeflex.*api_error/,
  
  // Development-only warnings that are expected
  /HTTPS.*development.*expected/,
  /Mixed Content.*development/,
];

// Patterns that should always be shown (critical errors)
const CRITICAL_PATTERNS = [
  /firebase/i,
  /authentication/i,
  /permission.*denied/i,
  /network.*error/i,
  /failed.*to.*fetch/i,
];

function shouldFilterMessage(message: string): boolean {
  // Never filter critical errors
  if (CRITICAL_PATTERNS.some(pattern => pattern.test(message))) {
    return false;
  }
  
  // Filter known non-critical patterns
  return FILTERED_PATTERNS.some(pattern => pattern.test(message));
}

function createFilteredConsoleMethod(originalMethod: ConsoleMethod, level: string): ConsoleMethod {
  return (...args: any[]) => {
    const message = args.join(' ');
    
    // In development, show more details but still filter noise
    if (process.env.NODE_ENV === 'development') {
      if (!shouldFilterMessage(message)) {
        originalMethod.apply(console, args);
      } else {
        // In development, show filtered messages with a prefix
        originalMethod.apply(console, [`[FILTERED-${level.toUpperCase()}]`, ...args]);
      }
    } else {
      // In production, completely filter out non-critical messages
      if (!shouldFilterMessage(message)) {
        originalMethod.apply(console, args);
      }
    }
  };
}

// Initialize console filtering
export function initializeConsoleFiltering() {
  // Only apply filtering in production or when explicitly enabled
  const shouldFilter = process.env.NODE_ENV === 'production' || 
                      localStorage.getItem('hive-filter-console') === 'true';
  
  if (shouldFilter) {
    console.error = createFilteredConsoleMethod(originalConsole.error, 'error');
    console.warn = createFilteredConsoleMethod(originalConsole.warn, 'warn');
    
    // Add a way to restore original console for debugging
    (window as any).__restoreConsole = () => {
      console.error = originalConsole.error;
      console.warn = originalConsole.warn;
      console.log = originalConsole.log;
      console.log('Console filtering disabled. Original console methods restored.');
    };
    
    // Add a way to enable filtering
    (window as any).__enableConsoleFiltering = () => {
      localStorage.setItem('hive-filter-console', 'true');
      location.reload();
    };
    
    console.log('Console filtering enabled. Use __restoreConsole() to disable.');
  }
}

// Utility to manually filter specific error types
export function filterStripeErrors() {
  const originalError = window.addEventListener;
  
  window.addEventListener = function(type: string, listener: any, options?: any) {
    if (type === 'error') {
      const wrappedListener = (event: ErrorEvent) => {
        const message = event.message || '';
        
        // Filter known Stripe errors
        if (shouldFilterMessage(message)) {
          event.preventDefault();
          return false;
        }
        
        return listener(event);
      };
      
      return originalError.call(this, type, wrappedListener, options);
    }
    
    return originalError.call(this, type, listener, options);
  };
}

// Export original console methods for debugging
export { originalConsole };
