import React, { useState, useEffect } from 'react';
import {
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  Database,
  RefreshCw,
  Server,
  Shield,
  Zap,
  TrendingUp,
  XCircle,
  Wifi,
  HardDrive,
  Users,
  Package,
  CreditCard
} from 'lucide-react';
import { httpsCallable } from 'firebase/functions';
import { functions } from '../../../firebase/config';
import AdminHealthService, { SystemHealthData, HealthAlert } from '../../../services/AdminHealthService';
import toast from 'react-hot-toast';

interface HealthData {
  services: {
    stripe: { status: string; latency?: string; error?: string };
    firebaseAuth: { status: string; latency?: string; error?: string };
    fcm: { status: string; note?: string; error?: string };
  };
  dataSync: {
    usersCount: number;
    ordersCount: number;
    listingsCount: number;
    notificationsCount: number;
    firestoreLatency: string;
  };
  integrity: {
    usersWithoutEmail: number;
    listingsWithoutPrice: number;
    listingsWithoutImage: number;
    listingsWithoutSeller: number;
    stuckTransactions: number;
    refundsWithHeldEscrow: number;
    totalIssues: number;
  };
  errors: {
    last24Hours: number;
    errorsByType: Record<string, number>;
    recentErrors: any[];
    criticalErrors: number;
  };
  latency: {
    firestore: number;
  };
  timestamp: any;
}

const AdminHealth: React.FC = () => {
  const [healthData, setHealthData] = useState<SystemHealthData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(false);
  const [alerts, setAlerts] = useState<HealthAlert[]>([]);
  const [healthScore, setHealthScore] = useState<number>(0);

  const fetchHealthData = async () => {
    try {
      setLoading(true);
      setError(null);

      const data = await AdminHealthService.performHealthCheck();
      setHealthData(data);
      setAlerts(AdminHealthService.getUnresolvedAlerts());
      setHealthScore(AdminHealthService.getHealthScore(data));

    } catch (err) {
      console.error('Health check error:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch health data');
      toast.error('Health check failed');
    } finally {
      setLoading(false);
    }
  };

  const exportHealthReport = () => {
    if (!healthData) return;

    const csv = AdminHealthService.exportHealthDataAsCSV(healthData);
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `hive-campus-health-report-${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
    toast.success('Health report exported successfully');
  };

  useEffect(() => {
    fetchHealthData();
  }, []);

  // Auto-refresh every 5 minutes if enabled
  useEffect(() => {
    if (!autoRefresh) return;
    
    const interval = setInterval(fetchHealthData, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, [autoRefresh]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-600 dark:text-green-400';
      case 'error': return 'text-red-600 dark:text-red-400';
      default: return 'text-yellow-600 dark:text-yellow-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy': return <CheckCircle className="h-5 w-5" />;
      case 'error': return <XCircle className="h-5 w-5" />;
      default: return <AlertTriangle className="h-5 w-5" />;
    }
  };

  const formatTimestamp = (timestamp: any) => {
    if (!timestamp) return 'N/A';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleString();
  };

  if (loading && !healthData) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <RefreshCw className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
            <p className="text-gray-600 dark:text-gray-400">Running system health check...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error && !healthData) {
    return (
      <div className="space-y-6">
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
          <div className="flex items-center">
            <XCircle className="h-6 w-6 text-red-600 dark:text-red-400 mr-3" />
            <div>
              <h3 className="text-lg font-medium text-red-800 dark:text-red-200">Health Check Failed</h3>
              <p className="text-red-600 dark:text-red-400 mt-1">{error}</p>
            </div>
          </div>
          <button
            onClick={fetchHealthData}
            className="mt-4 inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry Health Check
          </button>
        </div>
      </div>
    );
  }

  if (!healthData) return null;

  const overallHealth = healthData.integrity.totalIssues === 0 && 
                       healthData.errors.criticalErrors === 0 &&
                       Object.values(healthData.services).every(service => service.status === 'healthy');

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
            <Activity className="h-7 w-7 mr-2 text-blue-600" />
            System Health Dashboard
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Real-time monitoring of system health and performance
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={autoRefresh}
              onChange={(e) => setAutoRefresh(e.target.checked)}
              className="mr-2"
            />
            <span className="text-sm text-gray-600 dark:text-gray-400">Auto-refresh</span>
          </label>
          <div className="flex items-center space-x-3">
            <button
              onClick={exportHealthReport}
              disabled={!healthData}
              className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
            >
              <HardDrive className="h-4 w-4 mr-2" />
              Export Report
            </button>
            <button
              onClick={fetchHealthData}
              disabled={loading}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              {loading ? 'Checking...' : 'Refresh'}
            </button>
          </div>
        </div>
      </div>

      {/* Health Score & Status */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Overall Health Status */}
        <div className={`rounded-lg p-6 border-2 lg:col-span-2 ${
          overallHealth
            ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800'
            : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800'
        }`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              {overallHealth ? (
                <CheckCircle className="h-8 w-8 text-green-600 dark:text-green-400 mr-3" />
              ) : (
                <AlertTriangle className="h-8 w-8 text-red-600 dark:text-red-400 mr-3" />
              )}
              <div>
                <h2 className={`text-xl font-semibold ${
                  overallHealth ? 'text-green-800 dark:text-green-200' : 'text-red-800 dark:text-red-200'
                }`}>
                  System Status: {overallHealth ? 'Healthy' : 'Issues Detected'}
                </h2>
                <p className={`${
                  overallHealth ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
                }`}>
                  Last checked: {formatTimestamp(healthData.timestamp)}
                </p>
              </div>
            </div>
            <div className="text-right">
              <div className={`text-3xl font-bold ${
                healthScore >= 80 ? 'text-green-600' : healthScore >= 60 ? 'text-yellow-600' : 'text-red-600'
              }`}>
                {healthScore}%
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Health Score</p>
            </div>
          </div>
        </div>

        {/* Active Alerts */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-sm font-medium text-gray-900 dark:text-white flex items-center">
              <AlertTriangle className="h-4 w-4 mr-2" />
              Active Alerts ({alerts.length})
            </h3>
          </div>
          <div className="p-4">
            {alerts.length === 0 ? (
              <div className="text-center py-4">
                <CheckCircle className="h-8 w-8 text-green-400 mx-auto mb-2" />
                <p className="text-sm text-gray-500 dark:text-gray-400">No active alerts</p>
              </div>
            ) : (
              <div className="space-y-2 max-h-32 overflow-y-auto">
                {alerts.slice(0, 3).map((alert) => (
                  <div key={alert.id} className={`p-2 rounded text-xs ${
                    alert.type === 'critical'
                      ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                      : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
                  }`}>
                    <p className="font-medium">{alert.title}</p>
                    <p className="truncate">{alert.message}</p>
                  </div>
                ))}
                {alerts.length > 3 && (
                  <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
                    +{alerts.length - 3} more alerts
                  </p>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* System Services */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white flex items-center">
            <Server className="h-5 w-5 mr-2" />
            System Services
          </h3>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {Object.entries(healthData.services).map(([serviceName, service]) => (
              <div key={serviceName} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div className="flex items-center">
                  {serviceName === 'stripe' && <CreditCard className="h-5 w-5 mr-2" />}
                  {serviceName === 'firebaseAuth' && <Shield className="h-5 w-5 mr-2" />}
                  {serviceName === 'fcm' && <Wifi className="h-5 w-5 mr-2" />}
                  <div>
                    <p className="font-medium text-gray-900 dark:text-white capitalize">
                      {serviceName.replace(/([A-Z])/g, ' $1')}
                    </p>
                    {service.latency && (
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        Latency: {service.latency}
                      </p>
                    )}
                  </div>
                </div>
                <div className={`flex items-center ${getStatusColor(service.status)}`}>
                  {getStatusIcon(service.status)}
                  <span className="ml-1 text-sm font-medium capitalize">{service.status}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Data Sync & Integrity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Data Sync */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white flex items-center">
              <Database className="h-5 w-5 mr-2" />
              Data Sync Status
            </h3>
          </div>
          <div className="p-6 space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-600 dark:text-gray-400">Users</span>
              <span className="font-medium text-gray-900 dark:text-white">{healthData.dataSync.usersCount.toLocaleString()}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600 dark:text-gray-400">Orders</span>
              <span className="font-medium text-gray-900 dark:text-white">{healthData.dataSync.ordersCount.toLocaleString()}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600 dark:text-gray-400">Listings</span>
              <span className="font-medium text-gray-900 dark:text-white">{healthData.dataSync.listingsCount.toLocaleString()}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600 dark:text-gray-400">Firestore Latency</span>
              <span className={`font-medium ${
                healthData.latency.firestore < 1000 ? 'text-green-600' : 'text-yellow-600'
              }`}>
                {healthData.dataSync.firestoreLatency}
              </span>
            </div>
          </div>
        </div>

        {/* Database Integrity */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white flex items-center">
              <HardDrive className="h-5 w-5 mr-2" />
              Database Integrity
              {healthData.integrity.totalIssues > 0 && (
                <span className="ml-2 px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full">
                  {healthData.integrity.totalIssues} issues
                </span>
              )}
            </h3>
          </div>
          <div className="p-6 space-y-3">
            {Object.entries(healthData.integrity).map(([key, value]) => {
              if (key === 'totalIssues') return null;
              const hasIssue = typeof value === 'number' && value > 0;
              return (
                <div key={key} className="flex justify-between items-center">
                  <span className="text-gray-600 dark:text-gray-400 text-sm">
                    {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                  </span>
                  <span className={`font-medium ${hasIssue ? 'text-red-600' : 'text-green-600'}`}>
                    {typeof value === 'number' ? value : String(value)}
                  </span>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Error Monitoring */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white flex items-center">
            <AlertTriangle className="h-5 w-5 mr-2" />
            Error Monitoring (Last 24 Hours)
            {healthData.errors.criticalErrors > 0 && (
              <span className="ml-2 px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full">
                {healthData.errors.criticalErrors} critical
              </span>
            )}
          </h3>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Error Summary */}
            <div>
              <h4 className="font-medium text-gray-900 dark:text-white mb-3">Error Summary</h4>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600 dark:text-gray-400">Total Errors</span>
                  <span className={`font-medium ${
                    healthData.errors.last24Hours > 0 ? 'text-red-600' : 'text-green-600'
                  }`}>
                    {healthData.errors.last24Hours}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600 dark:text-gray-400">Critical Errors</span>
                  <span className={`font-medium ${
                    healthData.errors.criticalErrors > 0 ? 'text-red-600' : 'text-green-600'
                  }`}>
                    {healthData.errors.criticalErrors}
                  </span>
                </div>
              </div>

              {/* Error Types */}
              {Object.keys(healthData.errors.errorsByType).length > 0 && (
                <div className="mt-4">
                  <h5 className="text-sm font-medium text-gray-900 dark:text-white mb-2">Errors by Type</h5>
                  <div className="space-y-1">
                    {Object.entries(healthData.errors.errorsByType).map(([type, count]) => (
                      <div key={type} className="flex justify-between items-center text-sm">
                        <span className="text-gray-600 dark:text-gray-400">{type}</span>
                        <span className="font-medium text-gray-900 dark:text-white">{count}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Recent Errors */}
            <div>
              <h4 className="font-medium text-gray-900 dark:text-white mb-3">Recent Errors</h4>
              <div className="space-y-2 max-h-48 overflow-y-auto">
                {healthData.errors.recentErrors.length === 0 ? (
                  <div className="text-center py-4">
                    <CheckCircle className="h-8 w-8 text-green-400 mx-auto mb-2" />
                    <p className="text-sm text-gray-500 dark:text-gray-400">No recent errors</p>
                  </div>
                ) : (
                  healthData.errors.recentErrors.map((error, index) => (
                    <div key={index} className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                            {error.action || 'Unknown Action'}
                          </p>
                          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                            {formatTimestamp(error.timestamp)}
                          </p>
                        </div>
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          error.status === 'error'
                            ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                            : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400'
                        }`}>
                          {error.status || 'Warning'}
                        </span>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Performance Metrics */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white flex items-center">
            <TrendingUp className="h-5 w-5 mr-2" />
            Performance Metrics
          </h3>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <Clock className="h-8 w-8 text-blue-600 dark:text-blue-400 mx-auto mb-2" />
              <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {healthData.dataSync.firestoreLatency}
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">Firestore Latency</p>
            </div>
            <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <Zap className="h-8 w-8 text-green-600 dark:text-green-400 mx-auto mb-2" />
              <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                {Object.values(healthData.services).filter(s => s.status === 'healthy').length}/
                {Object.keys(healthData.services).length}
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">Services Healthy</p>
            </div>
            <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
              <Users className="h-8 w-8 text-purple-600 dark:text-purple-400 mx-auto mb-2" />
              <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                {((healthData.dataSync.usersCount - healthData.integrity.usersWithoutEmail) /
                  healthData.dataSync.usersCount * 100).toFixed(1)}%
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400">Data Quality</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminHealth;
