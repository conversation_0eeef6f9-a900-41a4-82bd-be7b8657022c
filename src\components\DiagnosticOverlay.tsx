import React, { useState, useEffect } from 'react';

interface DiagnosticInfo {
  timestamp: string;
  environment: string;
  firebaseConfig: any;
  envVars: Record<string, string>;
  errors: string[];
  status: 'loading' | 'success' | 'error';
}

const DiagnosticOverlay: React.FC = () => {
  const [diagnostics, setDiagnostics] = useState<DiagnosticInfo>({
    timestamp: new Date().toISOString(),
    environment: import.meta.env.MODE || 'unknown',
    firebaseConfig: {},
    envVars: {},
    errors: [],
    status: 'loading'
  });

  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Show diagnostic overlay if there's an error or if in development
    const shouldShow = import.meta.env.DEV || window.location.search.includes('debug=true');
    setIsVisible(shouldShow);

    // Collect diagnostic information
    const envVars: Record<string, string> = {};
    Object.keys(import.meta.env).forEach(key => {
      if (key.startsWith('VITE_')) {
        envVars[key] = import.meta.env[key] ? '✅ Present' : '❌ Missing';
      }
    });

    setDiagnostics(prev => ({
      ...prev,
      envVars,
      status: 'success'
    }));

    // Listen for errors
    const errorHandler = (event: ErrorEvent) => {
      setDiagnostics(prev => ({
        ...prev,
        errors: [...prev.errors, `${event.error?.message || event.message}`],
        status: 'error'
      }));
      setIsVisible(true);
    };

    window.addEventListener('error', errorHandler);
    return () => window.removeEventListener('error', errorHandler);
  }, []);

  if (!isVisible) return null;

  return (
    <div className="fixed top-0 left-0 w-full h-full bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-full overflow-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-2xl font-bold text-gray-900">
              🔍 Hive Campus Diagnostic Information
            </h2>
            <button
              onClick={() => setIsVisible(false)}
              className="text-gray-500 hover:text-gray-700 text-xl font-bold"
            >
              ×
            </button>
          </div>

          <div className="space-y-6">
            {/* Status */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-semibold text-lg mb-2">Status</h3>
              <div className="flex items-center space-x-2">
                <span className={`inline-block w-3 h-3 rounded-full ${
                  diagnostics.status === 'success' ? 'bg-green-500' :
                  diagnostics.status === 'error' ? 'bg-red-500' : 'bg-yellow-500'
                }`}></span>
                <span className="capitalize">{diagnostics.status}</span>
                <span className="text-gray-500">({diagnostics.timestamp})</span>
              </div>
            </div>

            {/* Environment */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-semibold text-lg mb-2">Environment</h3>
              <p><strong>Mode:</strong> {diagnostics.environment}</p>
              <p><strong>URL:</strong> {window.location.href}</p>
              <p><strong>User Agent:</strong> {navigator.userAgent}</p>
            </div>

            {/* Environment Variables */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-semibold text-lg mb-2">Environment Variables</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                {Object.entries(diagnostics.envVars).map(([key, value]) => (
                  <div key={key} className="flex justify-between">
                    <span className="font-mono text-sm">{key}:</span>
                    <span className="text-sm">{value}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Errors */}
            {diagnostics.errors.length > 0 && (
              <div className="bg-red-50 p-4 rounded-lg">
                <h3 className="font-semibold text-lg mb-2 text-red-800">Errors</h3>
                <ul className="space-y-1">
                  {diagnostics.errors.map((error, index) => (
                    <li key={index} className="text-red-700 text-sm font-mono">
                      {error}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Actions */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-semibold text-lg mb-2">Actions</h3>
              <div className="space-x-2">
                <button
                  onClick={() => window.location.reload()}
                  className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
                >
                  Reload Page
                </button>
                <button
                  onClick={() => {
                    localStorage.clear();
                    sessionStorage.clear();
                    window.location.reload();
                  }}
                  className="bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600"
                >
                  Clear Storage & Reload
                </button>
                <button
                  onClick={() => {
                    console.log('Diagnostic Info:', diagnostics);
                    alert('Diagnostic info logged to console');
                  }}
                  className="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600"
                >
                  Log to Console
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DiagnosticOverlay;
