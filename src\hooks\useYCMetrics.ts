import { useState, useEffect, useCallback } from 'react';
import { httpsCallable } from 'firebase/functions';
import { functions } from '../firebase/config';
import { useAuth } from './useAuth';

export interface DailyMetric {
  id: string;
  date: string;
  timestamp: any;
  dailyActiveUsers: number;
  weeklyActiveUsers: number;
  monthlyActiveUsers: number;
  newSignups: number;
  newListings: number;
  ordersPlaced: number;
  ordersCompleted: number;
  ordersDisputed: number;
  gmvToday: number;
  totalGMV: number;
  platformRevenueToday: number;
  totalRevenue: number;
  avgOrderTimeToFulfill: number;
  retentionDay1: number;
  retentionDay7: number;
  reeflexFlagsCount: number;
  fcmNotificationsSent: number;
  errorsLogged: number;
  totalUsers: number;
  totalListings: number;
  totalOrders: number;
}

export interface YCMetricsSummary {
  totalDays: number;
  dateRange: {
    start: string;
    end: string;
  };
  userGrowth: {
    totalNewSignups: number;
    avgDailySignups: number;
    peakDAU: number;
    avgDAU: number;
    currentMAU: number;
  };
  revenue: {
    totalGMV: number;
    totalPlatformRevenue: number;
    avgDailyGMV: number;
    avgDailyRevenue: number;
    peakDailyGMV: number;
  };
  orders: {
    totalOrdersPlaced: number;
    totalOrdersCompleted: number;
    totalOrdersDisputed: number;
    avgFulfillmentTime: number;
    completionRate: number;
  };
  content: {
    totalNewListings: number;
    avgDailyListings: number;
    totalListings: number;
  };
  retention: {
    avgDay1Retention: number;
    avgDay7Retention: number;
  };
  system: {
    totalNotificationsSent: number;
    totalErrorsLogged: number;
    totalReeflexFlags: number;
    avgDailyErrors: number;
  };
}

export interface YCMetricsData {
  dailyMetrics: DailyMetric[];
  summary: YCMetricsSummary;
  generatedAt: any;
}

export interface UseYCMetricsReturn {
  // Data
  metricsData: YCMetricsData | null;
  
  // Loading states
  isLoading: boolean;
  isRefreshing: boolean;
  
  // Error handling
  error: string | null;
  
  // Actions
  fetchMetrics: (days?: number) => Promise<void>;
  refreshMetrics: () => Promise<void>;
  clearError: () => void;
  
  // Export functions
  exportToCSV: () => void;
  exportSummaryToCSV: () => void;
}

export const useYCMetrics = (initialDays: number = 30): UseYCMetricsReturn => {
  const { currentUser, isAdmin } = useAuth();
  const [metricsData, setMetricsData] = useState<YCMetricsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchMetrics = useCallback(async (days: number = initialDays) => {
    if (!currentUser || !isAdmin) {
      setIsLoading(false);
      return;
    }

    try {
      setError(null);
      
      const getYCMetricsSummary = httpsCallable(functions, 'getYCMetricsSummary');
      const result = await getYCMetricsSummary({ days });
      const data = result.data as { success: boolean; data: YCMetricsData };
      
      if (data.success) {
        setMetricsData(data.data);
      } else {
        throw new Error('Failed to fetch YC metrics');
      }
    } catch (err) {
      console.error('Error fetching YC metrics:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch YC metrics');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  }, [currentUser, isAdmin, initialDays]);

  const refreshMetrics = useCallback(async () => {
    setIsRefreshing(true);
    await fetchMetrics();
  }, [fetchMetrics]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Export daily metrics to CSV
  const exportToCSV = useCallback(() => {
    if (!metricsData) return;

    const headers = [
      'Date',
      'Daily Active Users',
      'Weekly Active Users',
      'Monthly Active Users',
      'New Signups',
      'New Listings',
      'Orders Placed',
      'Orders Completed',
      'Orders Disputed',
      'GMV Today ($)',
      'Total GMV ($)',
      'Platform Revenue Today ($)',
      'Total Revenue ($)',
      'Avg Fulfillment Time (hrs)',
      'Day 1 Retention (%)',
      'Day 7 Retention (%)',
      'ReeFlex Flags',
      'FCM Notifications',
      'Errors Logged',
      'Total Users',
      'Total Listings',
      'Total Orders'
    ];

    const csvContent = [
      headers.join(','),
      ...metricsData.dailyMetrics.map(metric => [
        metric.date,
        metric.dailyActiveUsers,
        metric.weeklyActiveUsers,
        metric.monthlyActiveUsers,
        metric.newSignups,
        metric.newListings,
        metric.ordersPlaced,
        metric.ordersCompleted,
        metric.ordersDisputed,
        metric.gmvToday.toFixed(2),
        metric.totalGMV.toFixed(2),
        metric.platformRevenueToday.toFixed(2),
        metric.totalRevenue.toFixed(2),
        metric.avgOrderTimeToFulfill.toFixed(2),
        metric.retentionDay1.toFixed(2),
        metric.retentionDay7.toFixed(2),
        metric.reeflexFlagsCount,
        metric.fcmNotificationsSent,
        metric.errorsLogged,
        metric.totalUsers,
        metric.totalListings,
        metric.totalOrders
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `hive-campus-yc-metrics-${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  }, [metricsData]);

  // Export summary to CSV
  const exportSummaryToCSV = useCallback(() => {
    if (!metricsData) return;

    const { summary } = metricsData;
    
    const csvContent = [
      'Hive Campus - YC Metrics Summary',
      `Date Range: ${summary.dateRange.start} to ${summary.dateRange.end}`,
      `Total Days: ${summary.totalDays}`,
      '',
      'USER GROWTH',
      `Total New Signups,${summary.userGrowth.totalNewSignups}`,
      `Average Daily Signups,${summary.userGrowth.avgDailySignups.toFixed(2)}`,
      `Peak Daily Active Users,${summary.userGrowth.peakDAU}`,
      `Average Daily Active Users,${summary.userGrowth.avgDAU.toFixed(2)}`,
      `Current Monthly Active Users,${summary.userGrowth.currentMAU}`,
      '',
      'REVENUE',
      `Total GMV,$${summary.revenue.totalGMV.toFixed(2)}`,
      `Total Platform Revenue,$${summary.revenue.totalPlatformRevenue.toFixed(2)}`,
      `Average Daily GMV,$${summary.revenue.avgDailyGMV.toFixed(2)}`,
      `Average Daily Revenue,$${summary.revenue.avgDailyRevenue.toFixed(2)}`,
      `Peak Daily GMV,$${summary.revenue.peakDailyGMV.toFixed(2)}`,
      '',
      'ORDERS',
      `Total Orders Placed,${summary.orders.totalOrdersPlaced}`,
      `Total Orders Completed,${summary.orders.totalOrdersCompleted}`,
      `Total Orders Disputed,${summary.orders.totalOrdersDisputed}`,
      `Average Fulfillment Time (hrs),${summary.orders.avgFulfillmentTime.toFixed(2)}`,
      `Completion Rate,${summary.orders.completionRate.toFixed(2)}%`,
      '',
      'CONTENT',
      `Total New Listings,${summary.content.totalNewListings}`,
      `Average Daily Listings,${summary.content.avgDailyListings.toFixed(2)}`,
      `Total Listings,${summary.content.totalListings}`,
      '',
      'RETENTION',
      `Average Day 1 Retention,${summary.retention.avgDay1Retention.toFixed(2)}%`,
      `Average Day 7 Retention,${summary.retention.avgDay7Retention.toFixed(2)}%`,
      '',
      'SYSTEM HEALTH',
      `Total Notifications Sent,${summary.system.totalNotificationsSent}`,
      `Total Errors Logged,${summary.system.totalErrorsLogged}`,
      `Total ReeFlex Flags,${summary.system.totalReeflexFlags}`,
      `Average Daily Errors,${summary.system.avgDailyErrors.toFixed(2)}`
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `hive-campus-yc-summary-${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  }, [metricsData]);

  // Initial load
  useEffect(() => {
    fetchMetrics();
  }, [fetchMetrics]);

  return {
    metricsData,
    isLoading,
    isRefreshing,
    error,
    fetchMetrics,
    refreshMetrics,
    clearError,
    exportToCSV,
    exportSummaryToCSV
  };
};
