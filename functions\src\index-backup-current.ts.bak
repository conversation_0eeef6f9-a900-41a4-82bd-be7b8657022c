// Minimal functions index with refund functionality
import * as functions from 'firebase-functions/v1';
import * as admin from 'firebase-admin';
import Strip<PERSON> from 'stripe';

// Initialize Firebase Admin
admin.initializeApp();

// Types for listings
type ListingCondition = 'new' | 'like_new' | 'very_good' | 'good' | 'fair' | 'poor';
type ListingType = 'sell' | 'rent' | 'auction';
type ListingStatus = 'active' | 'sold' | 'inactive' | 'pending';

// Helper functions
const verifyAuth = async (context: functions.https.CallableContext): Promise<admin.auth.DecodedIdToken> => {
  if (!context.auth) {
    throw new functions.https.HttpsError(
      'unauthenticated',
      'The function must be called while authenticated.'
    );
  }
  return context.auth as unknown as admin.auth.DecodedIdToken;
};

const handleError = (error: unknown): never => {
  console.error('Function error:', error);

  if (error instanceof functions.https.HttpsError) {
    throw error;
  }

  const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
  throw new functions.https.HttpsError(
    'internal',
    errorMessage,
    error
  );
};

// Initialize Stripe
const stripe = new Stripe(functions.config().stripe?.api_key || process.env.STRIPE_API_KEY || '', {
  apiVersion: '2025-05-28.basil',
});

console.log('🚀 Minimal Firebase Functions with Refund loading...');

// Create a new listing
export const createListing = functions.https.onCall(async (data, context) => {
  try {
    console.log('Creating listing with data:', JSON.stringify(data, null, 2));
    const auth = await verifyAuth(context);

    const {
      title,
      description,
      price,
      category,
      condition,
      type,
      imageURLs
    } = data;

    // Validate required fields
    if (!title || !description || price === undefined || !category || !condition || !type) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'Missing required fields'
      );
    }

    // Validate listing type
    if (!['sell', 'rent', 'auction'].includes(type)) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'Invalid listing type. Must be one of: sell, rent, auction'
      );
    }

    // Validate condition
    if (!['new', 'like_new', 'very_good', 'good', 'fair', 'poor'].includes(condition)) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'Invalid condition. Must be one of: new, like_new, very_good, good, fair, poor'
      );
    }

    // Get user data to include university
    const userDoc = await admin.firestore().collection('users').doc(auth.uid).get();

    if (!userDoc.exists) {
      throw new functions.https.HttpsError(
        'not-found',
        'User not found'
      );
    }

    const userData = userDoc.data();
    console.log('User data:', JSON.stringify(userData, null, 2));

    // Get university from user data or extract from email
    let university = userData?.university;
    console.log('Initial university:', university);

    if (!university && userData?.email) {
      console.log('Extracting university from email:', userData.email);
      // Extract university from email domain as fallback
      const emailParts = userData.email.split('@');
      const domain = emailParts[1];
      university = domain.split('.')[0];
      // Capitalize university name
      university = university.charAt(0).toUpperCase() + university.slice(1);
      console.log('Extracted university:', university);

      // Update user profile with university
      await admin.firestore().collection('users').doc(auth.uid).update({
        university,
        updatedAt: admin.firestore.Timestamp.now()
      });
    } else if (!university && auth.token?.email) {
      console.log('Extracting university from auth token email:', auth.token.email);
      // Try to get email from auth token as fallback
      const emailParts = auth.token.email.split('@');
      const domain = emailParts[1];
      university = domain.split('.')[0];
      // Capitalize university name
      university = university.charAt(0).toUpperCase() + university.slice(1);
      console.log('Extracted university from token:', university);

      // Update user profile with university and email
      await admin.firestore().collection('users').doc(auth.uid).update({
        university,
        email: auth.token.email,
        updatedAt: admin.firestore.Timestamp.now()
      });
    }

    if (!university) {
      console.error('Unable to determine university. User data:', userData, 'Auth token:', auth.token);
      throw new functions.https.HttpsError(
        'failed-precondition',
        'Unable to determine university from user profile or email. Please update your profile.'
      );
    }

    console.log('Final university:', university);

    // Create the listing object
    const listing: any = {
      title,
      description,
      price: Number(price),
      category,
      condition: condition as ListingCondition,
      type: type as ListingType,
      ownerId: auth.uid,
      ownerName: userData?.name || 'Anonymous',
      university: university,
      imageURLs: imageURLs || [],
      status: 'active' as ListingStatus,
      visibility: data.visibility || 'university',
      createdAt: admin.firestore.Timestamp.now(),

      // Add delivery method fields
      deliveryMethod: data.deliveryMethod || 'in_person'
    };

    // Only add shippingOptions if it exists and has valid data
    if (data.shippingOptions && Object.keys(data.shippingOptions).length > 0) {
      listing.shippingOptions = data.shippingOptions;
    }

    // Add type-specific fields
    if (type === 'rent') {
      if (data.rentalPeriod) listing.rentalPeriod = data.rentalPeriod;
      if (data.weeklyPrice) listing.weeklyPrice = Number(data.weeklyPrice);
      if (data.monthlyPrice) listing.monthlyPrice = Number(data.monthlyPrice);
      if (data.startDate) listing.startDate = data.startDate;
      if (data.endDate) listing.endDate = data.endDate;
    }

    if (type === 'auction') {
      if (data.startingBid) listing.startingBid = Number(data.startingBid);
      if (data.auctionStartDate) listing.auctionStartDate = data.auctionStartDate;
      if (data.auctionStartTime) listing.auctionStartTime = data.auctionStartTime;
      if (data.auctionEndDate) listing.auctionEndDate = data.auctionEndDate;
      if (data.auctionEndTime) listing.auctionEndTime = data.auctionEndTime;
      if (data.auctionDuration) listing.auctionDuration = data.auctionDuration;
    }

    // Add to Firestore
    console.log('Adding listing to Firestore:', JSON.stringify(listing, null, 2));
    const docRef = await admin.firestore().collection('listings').add(listing);
    console.log('Listing created successfully with ID:', docRef.id);

    return {
      success: true,
      data: {
        id: docRef.id,
        ...listing
      }
    };
  } catch (error) {
    return handleError(error);
  }
});

// Get detailed payment information from Stripe
export const getPaymentDetails = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
  })
  .https.onCall(async (data, context) => {
    try {
      // Verify authentication
      if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
      }

      const { orderId } = data;
      if (!orderId) {
        throw new functions.https.HttpsError('invalid-argument', 'Order ID is required');
      }

      // Get the order from Firestore
      const orderDoc = await admin.firestore().collection('orders').doc(orderId).get();
      if (!orderDoc.exists) {
        throw new functions.https.HttpsError('not-found', 'Order not found');
      }

      const orderData = orderDoc.data();

      // Verify user has access to this order
      if (orderData?.buyerId !== context.auth.uid && orderData?.sellerId !== context.auth.uid) {
        throw new functions.https.HttpsError('permission-denied', 'Access denied');
      }

      const paymentDetails: any = {
        orderId: orderId,
        orderAmount: orderData?.amount || 0,
        commissionAmount: orderData?.commissionAmount || 0,
        sellerAmount: orderData?.sellerAmount || 0,
        walletAmountUsed: orderData?.walletAmountUsed || 0,
        status: orderData?.status || 'unknown',
        createdAt: orderData?.createdAt,
        paymentCompletedAt: orderData?.paymentCompletedAt,
      };

      // Fetch Stripe Payment Intent details if available
      if (orderData?.stripePaymentIntentId) {
        try {
          const paymentIntent = await stripe.paymentIntents.retrieve(orderData.stripePaymentIntentId, {
            expand: ['payment_method']
          });

          paymentDetails.stripePaymentIntent = {
            id: paymentIntent.id,
            amount: paymentIntent.amount,
            currency: paymentIntent.currency,
            status: paymentIntent.status,
            created: paymentIntent.created,
            description: paymentIntent.description,
            receipt_email: paymentIntent.receipt_email,
            payment_method_types: paymentIntent.payment_method_types,
          };

          // Get payment method details if available
          if (paymentIntent.payment_method && typeof paymentIntent.payment_method === 'object') {
            const paymentMethod = paymentIntent.payment_method as any;
            paymentDetails.paymentMethod = {
              type: paymentMethod.type,
              card: paymentMethod.card ? {
                brand: paymentMethod.card.brand,
                last4: paymentMethod.card.last4,
                exp_month: paymentMethod.card.exp_month,
                exp_year: paymentMethod.card.exp_year,
                funding: paymentMethod.card.funding,
              } : null,
            };
          }

          // Get charges information separately
          try {
            const charges = await stripe.charges.list({
              payment_intent: paymentIntent.id,
              limit: 1
            });

            if (charges.data.length > 0) {
              const charge = charges.data[0];
              paymentDetails.charge = {
                id: charge.id,
                amount: charge.amount,
                currency: charge.currency,
                status: charge.status,
                created: charge.created,
                paid: charge.paid,
                refunded: charge.refunded,
                amount_refunded: charge.amount_refunded,
                receipt_url: charge.receipt_url,
                billing_details: charge.billing_details,
                outcome: charge.outcome,
              };
            }
          } catch (chargeError) {
            console.error('Error fetching charges:', chargeError);
          }
        } catch (stripeError) {
          console.error('Error fetching Stripe payment intent:', stripeError);
          paymentDetails.stripeError = 'Unable to fetch payment details from Stripe';
        }
      }

      // Fetch Stripe Session details if available
      if (orderData?.stripeSessionId) {
        try {
          const session = await stripe.checkout.sessions.retrieve(orderData.stripeSessionId);
          paymentDetails.stripeSession = {
            id: session.id,
            payment_status: session.payment_status,
            status: session.status,
            amount_total: session.amount_total,
            currency: session.currency,
            created: session.created,
            expires_at: session.expires_at,
            customer_email: session.customer_details?.email,
            customer_name: session.customer_details?.name,
            payment_method_types: session.payment_method_types,
          };
        } catch (stripeError) {
          console.error('Error fetching Stripe session:', stripeError);
          paymentDetails.sessionError = 'Unable to fetch session details from Stripe';
        }
      }

      return paymentDetails;
    } catch (error) {
      console.error('Error in getPaymentDetails:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new functions.https.HttpsError('internal', errorMessage);
    }
  });

// Test function
export const testMinimalRefund = functions
  .https.onRequest(async (_req, res) => {
    res.json({
      success: true,
      message: 'Minimal refund functions working',
      timestamp: new Date().toISOString()
    });
  });

// Refund transaction function - simplified version without Stripe for now
export const refundTransaction = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 60,
  })
  .https.onCall(async (data, context) => {
    try {
      // Check authentication
      if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
      }

      // Verify admin role
      const userDoc = await admin.firestore().collection('users').doc(context.auth.uid).get();
      if (!userDoc.exists || userDoc.data()?.role !== 'admin') {
        throw new functions.https.HttpsError('permission-denied', 'Admin access required');
      }

      const { orderId, amount, reason } = data;

      if (!orderId) {
        throw new functions.https.HttpsError('invalid-argument', 'Order ID is required');
      }

      // Get order details
      const orderRef = admin.firestore().collection('orders').doc(orderId);
      const orderDoc = await orderRef.get();

      if (!orderDoc.exists) {
        throw new functions.https.HttpsError('not-found', 'Order not found');
      }

      const orderData = orderDoc.data();

      // For now, just update the order status without Stripe processing
      // TODO: Add Stripe refund processing later
      await orderRef.update({
        status: 'refunded',
        refundAmount: amount || orderData?.totalAmount || 0,
        refundReason: reason || 'Admin refund',
        refundedAt: admin.firestore.Timestamp.now(),
        refundedBy: context.auth.uid,
        updatedAt: admin.firestore.Timestamp.now()
      });

      // Update listing back to active if needed
      if (orderData?.listingId) {
        const listingRef = admin.firestore().collection('listings').doc(orderData.listingId);
        const listingDoc = await listingRef.get();

        if (listingDoc.exists && listingDoc.data()?.status === 'sold') {
          await listingRef.update({
            status: 'active',
            updatedAt: admin.firestore.Timestamp.now()
          });
        }
      }

      console.log(`✅ Refund processed for order: ${orderId}`);

      return {
        success: true,
        message: 'Refund processed successfully (Stripe processing will be added later)',
        orderId: orderId,
        refundAmount: amount || orderData?.totalAmount || 0
      };

    } catch (error) {
      console.error('Error processing refund:', error);
      throw error;
    }
  });

// Stripe webhook for payment processing
export const stripeWebhook = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 60,
  })
  .https.onRequest(async (req, res) => {
    try {
      console.log('🔗 Stripe webhook received');

      if (req.method !== 'POST') {
        res.status(405).send('Method not allowed');
        return;
      }

      const sig = req.headers['stripe-signature'] as string;
      const webhookSecret = 'whsec_rEz20Kue3bkGHfmnaZilP01s614ULBQb'; // Your webhook secret

      if (!sig) {
        console.error('No Stripe signature found');
        res.status(400).send('No Stripe signature');
        return;
      }

      let event;
      try {
        event = stripe.webhooks.constructEvent(req.body, sig, webhookSecret);
      } catch (err: any) {
        console.error('Webhook signature verification failed:', err.message);
        res.status(400).send(`Webhook Error: ${err.message}`);
        return;
      }

      console.log(`🎯 Processing webhook event: ${event.type}`);

      // Handle the event
      switch (event.type) {
        case 'checkout.session.completed':
          const session = event.data.object as any;
          console.log('💳 Payment succeeded for session:', session.id);

          // Update order status
          const orderId = session.metadata?.orderId;
          if (orderId) {
            await admin.firestore().collection('orders').doc(orderId).update({
              status: 'paid',
              stripePaymentIntentId: session.payment_intent,
              paidAt: admin.firestore.Timestamp.now(),
              updatedAt: admin.firestore.Timestamp.now()
            });

            // Update listing status to sold
            const listingId = session.metadata?.listingId;
            if (listingId) {
              await admin.firestore().collection('listings').doc(listingId).update({
                status: 'sold',
                soldAt: admin.firestore.Timestamp.now(),
                updatedAt: admin.firestore.Timestamp.now()
              });
            }

            console.log(`✅ Order ${orderId} marked as paid`);
          }
          break;

        case 'payment_intent.payment_failed':
          const paymentIntent = event.data.object as any;
          console.log('❌ Payment failed for payment intent:', paymentIntent.id);
          break;

        default:
          console.log(`Unhandled event type: ${event.type}`);
      }

      res.status(200).json({ received: true });

    } catch (error) {
      console.error('❌ Webhook error:', error);
      res.status(500).send('Webhook failed');
    }
  });

// Stripe API for checkout sessions
export const stripeApi = functions
  .runWith({
    memory: '512MB',
    timeoutSeconds: 60,
  })
  .https.onRequest(async (req, res) => {
    // Enable CORS for all origins
    res.set('Access-Control-Allow-Origin', '*');
    res.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
    res.set('Access-Control-Allow-Credentials', 'true');

    if (req.method === 'OPTIONS') {
      res.status(200).send();
      return;
    }

    try {
      const path = req.path;
      console.log(`🔗 StripeApi request: ${req.method} ${path}`);

      if (path === '/create-checkout-session' && req.method === 'POST') {
        // Handle checkout session creation
        try {
          // Check authorization
          const authHeader = req.headers.authorization;
          if (!authHeader || !authHeader.startsWith('Bearer ')) {
            res.status(401).json({ error: 'Unauthorized - Missing or invalid authorization header' });
            return;
          }

          const token = authHeader.split('Bearer ')[1];
          const decodedToken = await admin.auth().verifyIdToken(token);
          const buyerId = decodedToken.uid;

          // Get request data
          const { listingId, useWalletBalance = false, orderDetails } = req.body;

          if (!listingId) {
            res.status(400).json({ error: 'Listing ID is required' });
            return;
          }

          console.log(`🛒 Creating checkout for listing: ${listingId}, buyer: ${buyerId}`);

          // Get listing details
          const listingDoc = await admin.firestore().collection('listings').doc(listingId).get();
          if (!listingDoc.exists) {
            res.status(404).json({ error: 'Listing not found' });
            return;
          }

          const listing = listingDoc.data();
          if (!listing) {
            res.status(404).json({ error: 'Listing data not found' });
            return;
          }

          // Calculate amounts
          const itemPrice = listing.price;
          const shippingCost = orderDetails?.shippingFee || 0;
          const walletBalanceUsed = orderDetails?.appliedWalletCredit || 0;
          const totalBeforeWallet = itemPrice + shippingCost;
          const finalAmount = Math.max(0, totalBeforeWallet - walletBalanceUsed);

          console.log(`💰 Pricing: Item: $${itemPrice}, Shipping: $${shippingCost}, Wallet: $${walletBalanceUsed}, Final: $${finalAmount}`);

          // Create order document
          const orderId = admin.firestore().collection('orders').doc().id;
          const orderData = {
            id: orderId,
            listingId,
            buyerId,
            sellerId: listing.ownerId,
            status: 'pending',
            totalAmount: finalAmount,
            itemPrice,
            shippingCost,
            walletBalanceUsed,
            originalTotal: totalBeforeWallet,
            createdAt: admin.firestore.Timestamp.now(),
            updatedAt: admin.firestore.Timestamp.now(),
            deliveryMethod: orderDetails?.deliveryMethod || listing.deliveryMethod || 'in_person',
            shippingAddress: orderDetails?.shippingAddress || null
          };

          await admin.firestore().collection('orders').doc(orderId).set(orderData);

          // If final amount is 0 (fully paid with wallet), mark as paid
          if (finalAmount <= 0) {
            await admin.firestore().collection('orders').doc(orderId).update({
              status: 'paid',
              paymentMethod: 'wallet',
              paidAt: admin.firestore.Timestamp.now()
            });

            res.status(200).json({
              success: true,
              sessionId: null,
              sessionUrl: null,
              orderId,
              message: 'Order paid with wallet balance',
              finalAmount: 0
            });
            return;
          }

          // Create Stripe checkout session
          const session = await stripe.checkout.sessions.create({
            payment_method_types: ['card'],
            line_items: [{
              price_data: {
                currency: 'usd',
                product_data: {
                  name: `${listing.title}${walletBalanceUsed > 0 ? ` (after $${walletBalanceUsed} wallet credit)` : ''}`,
                  description: listing.description || 'Hive Campus item',
                },
                unit_amount: Math.round(finalAmount * 100), // Convert to cents
              },
              quantity: 1,
            }],
            mode: 'payment',
            success_url: `http://localhost:5174/order-success?session_id={CHECKOUT_SESSION_ID}&order_id=${orderId}`,
            cancel_url: `http://localhost:5174/listing/${listingId}`,
            metadata: {
              orderId,
              listingId,
              buyerId,
              sellerId: listing.ownerId,
              walletAmountUsed: walletBalanceUsed.toString(),
              originalTotal: totalBeforeWallet.toString(),
              finalAmount: finalAmount.toString(),
            },
          });

          // Update order with session ID
          await admin.firestore().collection('orders').doc(orderId).update({
            stripeSessionId: session.id,
            updatedAt: admin.firestore.Timestamp.now()
          });

          console.log(`✅ Checkout session created: ${session.id}`);

          res.status(200).json({
            success: true,
            sessionId: session.id,
            sessionUrl: session.url,
            orderId,
            finalAmount
          });

        } catch (checkoutError: any) {
          console.error('❌ Checkout session error:', checkoutError);
          res.status(500).json({
            error: 'Failed to create checkout session',
            details: checkoutError.message
          });
        }
      } else {
        res.status(404).json({ error: 'Endpoint not found' });
      }
    } catch (error: any) {
      console.error('❌ Error in stripeApi:', error);
      res.status(500).json({ error: 'Internal server error', details: error.message });
    }
  });

// Get Stripe Connect onboarding link
export const getStripeConnectOnboardingLink = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
  })
  .https.onCall(async (data, context) => {
    try {
      // Check authentication
      if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
      }

      const { refreshUrl, returnUrl } = data;
      const userId = context.auth.uid;

      console.log(`🔗 Getting onboarding link for user: ${userId}`);

      // Get connect account from Firestore
      const connectAccountDoc = await admin.firestore().collection('connectAccounts').doc(userId).get();

      if (!connectAccountDoc.exists) {
        throw new functions.https.HttpsError('not-found', 'No Stripe Connect account found. Please create an account first.');
      }

      const connectAccount = connectAccountDoc.data();
      const stripeAccountId = connectAccount?.stripeAccountId;

      if (!stripeAccountId) {
        throw new functions.https.HttpsError('not-found', 'No Stripe account ID found');
      }

      // If already onboarded, return dashboard URL instead
      if (connectAccount?.isOnboarded) {
        return {
          onboardingUrl: connectAccount.dashboardUrl || `https://dashboard.stripe.com/connect/accounts/${stripeAccountId}`,
          isOnboarded: true
        };
      }

      // Create fresh onboarding link
      const baseUrl = 'http://localhost:5174'; // Use current dev server URL
      const accountLink = await stripe.accountLinks.create({
        account: stripeAccountId,
        refresh_url: refreshUrl || `${baseUrl}/settings/payment?stripe_refresh=true`,
        return_url: returnUrl || `${baseUrl}/settings/payment?stripe_success=true`,
        type: 'account_onboarding',
      });

      // Update Firestore with new onboarding URL
      await admin.firestore().collection('connectAccounts').doc(userId).update({
        onboardingUrl: accountLink.url,
        updatedAt: admin.firestore.Timestamp.now(),
      });

      console.log(`✅ Created onboarding link: ${accountLink.url}`);

      return {
        onboardingUrl: accountLink.url,
        isOnboarded: false
      };

    } catch (error) {
      console.error('Error in getStripeConnectOnboardingLink:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new functions.https.HttpsError('internal', errorMessage);
    }
  });

// Create Stripe Connect account
export const createStripeConnectAccount = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 60,
  })
  .https.onCall(async (data, context) => {
    try {
      // Check authentication
      if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
      }

      const { accountType } = data;
      const userId = context.auth.uid;

      if (!accountType || !['student', 'merchant'].includes(accountType)) {
        throw new functions.https.HttpsError('invalid-argument', 'Valid account type is required');
      }

      console.log(`🏦 Creating Stripe Connect account for user: ${userId}, type: ${accountType}`);

      // Check if user already has a Connect account
      const existingAccountDoc = await admin.firestore().collection('connectAccounts').doc(userId).get();
      if (existingAccountDoc.exists) {
        const existingAccount = existingAccountDoc.data();

        // If account exists but not onboarded, return existing onboarding URL
        if (!existingAccount?.isOnboarded && existingAccount?.onboardingUrl) {
          return {
            accountId: existingAccount.stripeAccountId,
            onboardingUrl: existingAccount.onboardingUrl,
            message: 'Using existing account'
          };
        }

        // If already onboarded, return success
        if (existingAccount?.isOnboarded) {
          throw new functions.https.HttpsError('already-exists', 'User already has a fully onboarded Connect account');
        }
      }

      // Get user data
      const userDoc = await admin.firestore().collection('users').doc(userId).get();
      if (!userDoc.exists) {
        throw new functions.https.HttpsError('not-found', 'User not found');
      }

      const user = userDoc.data();

      // Create a Stripe Connect Express account
      const account = await stripe.accounts.create({
        type: 'express',
        country: 'US',
        email: user?.email,
        capabilities: {
          card_payments: { requested: true },
          transfers: { requested: true },
        },
        business_type: accountType === 'merchant' ? 'company' : 'individual',
        metadata: {
          userId,
          accountType
        }
      });

      // Create an account link for onboarding
      const baseUrl = 'http://localhost:5174'; // Use current dev server URL
      const accountLink = await stripe.accountLinks.create({
        account: account.id,
        refresh_url: `${baseUrl}/settings/payment?stripe_refresh=true`,
        return_url: `${baseUrl}/settings/payment?stripe_success=true`,
        type: 'account_onboarding',
      });

      // Store the Connect account in Firestore
      await admin.firestore().collection('connectAccounts').doc(userId).set({
        userId: userId,
        stripeAccountId: account.id,
        accountType: accountType,
        isOnboarded: false,
        chargesEnabled: false,
        payoutsEnabled: false,
        detailsSubmitted: false,
        onboardingUrl: accountLink.url,
        createdAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now(),
      });

      console.log(`✅ Created Stripe Connect account ${account.id} for user ${userId}`);

      return {
        accountId: account.id,
        onboardingUrl: accountLink.url,
        message: 'Connect account created successfully'
      };

    } catch (error) {
      console.error('Error in createStripeConnectAccount:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new functions.https.HttpsError('internal', errorMessage);
    }
  });
