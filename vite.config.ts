import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { VitePWA } from 'vite-plugin-pwa';
import type { Plugin } from 'vite';

// Plugin to conditionally set CSP based on environment
const conditionalCSP = (): Plugin => {
  return {
    name: 'conditional-csp',
    transformIndexHtml: {
      enforce: 'pre',
      transform(html, ctx) {
        const isDev = ctx.server?.config.command === 'serve';

        if (isDev) {
          // Development CSP - allow localhost connections
          const devCSP = `default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' 'wasm-unsafe-eval' https://www.gstatic.com https://www.googleapis.com https://apis.google.com https://www.googletagmanager.com https://js.stripe.com https://api.reeflex.ai https://browser.sentry-cdn.com https://*.sentry-cdn.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: blob: https:; media-src 'self' blob:; connect-src 'self' http://localhost:* ws://localhost:* wss://localhost:* https://*.googleapis.com https://*.firebase.googleapis.com https://*.firebaseio.com https://*.cloudfunctions.net https://firestore.googleapis.com https://api.stripe.com https://connect.stripe.com https://api.reeflex.ai https://sentry.io https://*.sentry.io https://www.google-analytics.com https://www.googletagmanager.com wss://*.firebaseio.com; frame-src 'self' https://js.stripe.com https://checkout.stripe.com https://connect.stripe.com https://*.stripe.com https://*.firebaseapp.com https://*.google.com https://accounts.google.com; object-src 'none'; base-uri 'self'; form-action 'self' https://checkout.stripe.com https://connect.stripe.com`;

          return html.replace(
            /content="[^"]*"/,
            `content="${devCSP}"`
          );
        } else {
          // Production CSP - more restrictive
          const prodCSP = `default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' 'wasm-unsafe-eval' https://www.gstatic.com https://www.googleapis.com https://apis.google.com https://www.googletagmanager.com https://js.stripe.com https://api.reeflex.ai https://browser.sentry-cdn.com https://*.sentry-cdn.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: blob: https:; media-src 'self' blob:; connect-src 'self' https://*.googleapis.com https://*.firebase.googleapis.com https://*.firebaseio.com https://*.cloudfunctions.net https://firestore.googleapis.com https://api.stripe.com https://connect.stripe.com https://api.reeflex.ai https://sentry.io https://*.sentry.io https://www.google-analytics.com https://www.googletagmanager.com wss://*.firebaseio.com; frame-src 'self' https://js.stripe.com https://checkout.stripe.com https://connect.stripe.com https://*.stripe.com https://*.firebaseapp.com https://*.google.com https://accounts.google.com; object-src 'none'; base-uri 'self'; form-action 'self' https://checkout.stripe.com https://connect.stripe.com; upgrade-insecure-requests; block-all-mixed-content`;

          return html.replace(
            /content="[^"]*"/,
            `content="${prodCSP}"`
          );
        }
      }
    }
  };
};

// https://vitejs.dev/config/
export default defineConfig({
  define: {
    global: 'globalThis',
    'process.env': {},
  },
  plugins: [
    react(),
    // conditionalCSP(), // Temporarily disabled
    VitePWA({
      // Disable service worker in development to prevent conflicts
      disable: process.env.NODE_ENV === 'development',
      registerType: 'autoUpdate',

      // Use generateSW instead of injectManifest to avoid manifest injection issues
      strategies: 'generateSW',

      // Use our own manifest.json
      manifest: false,

      // Include these assets in the PWA
      includeAssets: [
        'favicon.ico',
        'robots.txt',
        'offline.html'
      ],

      // Simplified workbox options for generateSW
      workbox: {
        globPatterns: ['**/*.{js,css,html,ico,png,svg,webp}'],
        globIgnores: ['**/node_modules/**/*', '**/.vite/**/*'],
        maximumFileSizeToCacheInBytes: 5000000,
        navigateFallback: '/index.html',
        navigateFallbackDenylist: [/^\/_/, /\/[^/?]+\.[^/]+$/, /\/node_modules\//]
      },

      // Dev options - completely disable in development
      devOptions: {
        enabled: false,
        type: 'module'
      }
    }),
  ],
  optimizeDeps: {
    include: [
      'hoist-non-react-statics',
      'react-hot-toast',
      'date-fns',
      'react-router-dom'
    ],
    exclude: [
      'lucide-react'
    ],
    esbuildOptions: {
      define: {
        global: 'globalThis'
      }
    },
    force: true
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: (id) => {
          // Vendor chunks
          if (id.includes('node_modules')) {
            // Firebase related chunks
            if (id.includes('firebase')) {
              return 'firebase';
            }
            // React related chunks
            if (id.includes('react') || id.includes('react-dom') || id.includes('react-router')) {
              return 'react';
            }
            // UI/Design related chunks
            if (id.includes('lucide-react')) {
              return 'ui';
            }
            // Sentry related chunks
            if (id.includes('@sentry')) {
              return 'sentry';
            }
            // Date utilities
            if (id.includes('date-fns')) {
              return 'date-utils';
            }
            // Other vendor libraries
            return 'vendor';
          }
          
          // Split by route/page
          if (id.includes('/pages/admin/')) {
            return 'admin';
          }
          if (id.includes('/pages/merchant/')) {
            return 'merchant';
          }
          if (id.includes('/pages/')) {
            return 'pages';
          }
          if (id.includes('/components/')) {
            return 'components';
          }
        }
      }
    },
    chunkSizeWarningLimit: 1000,
    // Enable compression
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true, // Remove console.logs in production
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.info', 'console.debug'],
      },
      mangle: {
        safari10: true,
      },
    },
    // Source maps for production debugging
    sourcemap: true,
    // Enable CSS code splitting
    cssCodeSplit: true,
  },
  server: {
    // Configure the development server to better handle WebSocket connections
    hmr: {
      port: 5173,
      // Handle WebSocket connection errors more gracefully
      overlay: false
    },
    // Ensure the port is properly set
    port: 5173,
    host: true,
    // Note: HTTPS is required for production Stripe.js integration
    // Development can use HTTP for testing
  },
  // Clear cache on startup
  clearScreen: false,
});
