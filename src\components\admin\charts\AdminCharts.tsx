import React from 'react';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer
} from 'recharts';

interface ChartData {
  name: string;
  value: number;
  [key: string]: any;
}

interface AdminChartsProps {
  userGrowthData?: ChartData[];
  revenueData?: ChartData[];
  categoryData?: ChartData[];
  universityData?: ChartData[];
}

const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4'];

export const UserGrowthChart: React.FC<{ data: ChartData[] }> = ({ data }) => (
  <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">User Growth</h3>
    <ResponsiveContainer width="100%" height={300}>
      <AreaChart data={data}>
        <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
        <XAxis 
          dataKey="name" 
          className="text-gray-600 dark:text-gray-400"
          fontSize={12}
        />
        <YAxis 
          className="text-gray-600 dark:text-gray-400"
          fontSize={12}
        />
        <Tooltip 
          contentStyle={{
            backgroundColor: 'var(--tooltip-bg)',
            border: '1px solid var(--tooltip-border)',
            borderRadius: '8px',
            color: 'var(--tooltip-color)'
          }}
        />
        <Area
          type="monotone"
          dataKey="users"
          stroke="#3B82F6"
          fill="#3B82F6"
          fillOpacity={0.1}
          strokeWidth={2}
        />
      </AreaChart>
    </ResponsiveContainer>
  </div>
);

export const RevenueChart: React.FC<{ data: ChartData[] }> = ({ data }) => (
  <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Revenue Trends</h3>
    <ResponsiveContainer width="100%" height={300}>
      <LineChart data={data}>
        <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
        <XAxis 
          dataKey="name" 
          className="text-gray-600 dark:text-gray-400"
          fontSize={12}
        />
        <YAxis 
          className="text-gray-600 dark:text-gray-400"
          fontSize={12}
        />
        <Tooltip 
          contentStyle={{
            backgroundColor: 'var(--tooltip-bg)',
            border: '1px solid var(--tooltip-border)',
            borderRadius: '8px',
            color: 'var(--tooltip-color)'
          }}
          formatter={(value: any) => [`$${value.toLocaleString()}`, 'Revenue']}
        />
        <Line
          type="monotone"
          dataKey="revenue"
          stroke="#10B981"
          strokeWidth={3}
          dot={{ fill: '#10B981', strokeWidth: 2, r: 4 }}
          activeDot={{ r: 6, stroke: '#10B981', strokeWidth: 2 }}
        />
      </LineChart>
    </ResponsiveContainer>
  </div>
);

export const CategoryChart: React.FC<{ data: ChartData[] }> = ({ data }) => (
  <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Listings by Category</h3>
    <ResponsiveContainer width="100%" height={300}>
      <PieChart>
        <Pie
          data={data}
          cx="50%"
          cy="50%"
          labelLine={false}
          label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
          outerRadius={80}
          fill="#8884d8"
          dataKey="value"
        >
          {data.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
          ))}
        </Pie>
        <Tooltip 
          contentStyle={{
            backgroundColor: 'var(--tooltip-bg)',
            border: '1px solid var(--tooltip-border)',
            borderRadius: '8px',
            color: 'var(--tooltip-color)'
          }}
        />
      </PieChart>
    </ResponsiveContainer>
  </div>
);

export const UniversityChart: React.FC<{ data: ChartData[] }> = ({ data }) => (
  <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Top Universities</h3>
    <ResponsiveContainer width="100%" height={300}>
      <BarChart data={data} layout="horizontal">
        <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
        <XAxis 
          type="number"
          className="text-gray-600 dark:text-gray-400"
          fontSize={12}
        />
        <YAxis 
          type="category"
          dataKey="name"
          className="text-gray-600 dark:text-gray-400"
          fontSize={12}
          width={120}
        />
        <Tooltip 
          contentStyle={{
            backgroundColor: 'var(--tooltip-bg)',
            border: '1px solid var(--tooltip-border)',
            borderRadius: '8px',
            color: 'var(--tooltip-color)'
          }}
        />
        <Bar
          dataKey="users"
          fill="#8B5CF6"
          radius={[0, 4, 4, 0]}
        />
      </BarChart>
    </ResponsiveContainer>
  </div>
);

export const OrdersChart: React.FC<{ data: ChartData[] }> = ({ data }) => (
  <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Orders Overview</h3>
    <ResponsiveContainer width="100%" height={300}>
      <BarChart data={data}>
        <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
        <XAxis 
          dataKey="name" 
          className="text-gray-600 dark:text-gray-400"
          fontSize={12}
        />
        <YAxis 
          className="text-gray-600 dark:text-gray-400"
          fontSize={12}
        />
        <Tooltip 
          contentStyle={{
            backgroundColor: 'var(--tooltip-bg)',
            border: '1px solid var(--tooltip-border)',
            borderRadius: '8px',
            color: 'var(--tooltip-color)'
          }}
        />
        <Bar dataKey="completed" fill="#10B981" name="Completed" />
        <Bar dataKey="pending" fill="#F59E0B" name="Pending" />
        <Bar dataKey="cancelled" fill="#EF4444" name="Cancelled" />
        <Legend />
      </BarChart>
    </ResponsiveContainer>
  </div>
);

const AdminCharts: React.FC<AdminChartsProps> = ({
  userGrowthData = [],
  revenueData = [],
  categoryData = [],
  universityData = []
}) => {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <UserGrowthChart data={userGrowthData} />
        <RevenueChart data={revenueData} />
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <CategoryChart data={categoryData} />
        <UniversityChart data={universityData} />
      </div>
    </div>
  );
};

export default AdminCharts;
