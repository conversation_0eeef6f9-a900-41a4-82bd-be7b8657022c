import React, { useEffect, useRef, useState } from 'react';
import { Link } from 'react-router-dom';
import * as THREE from 'three';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { EffectComposer } from 'three/addons/postprocessing/EffectComposer.js';
import { RenderPass } from 'three/addons/postprocessing/RenderPass.js';
import { UnrealBloomPass } from 'three/addons/postprocessing/UnrealBloomPass.js';
import AnimatedButton from '../components/AnimatedButton';
import '../styles/HiveCampusLanding.css';

gsap.registerPlugin(ScrollTrigger);

const HiveCampusLanding: React.FC = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const subtitleRef = useRef<HTMLDivElement>(null);
  const scrollProgressRef = useRef<HTMLDivElement>(null);
  const menuRef = useRef<HTMLDivElement>(null);

  const smoothCameraPos = useRef({ x: 0, y: 30, z: 100 });
  
  const [_scrollProgress, _setScrollProgress] = useState(0);
  const [_currentSection, _setCurrentSection] = useState(0);
  const [isReady, setIsReady] = useState(false);
  const totalSections = 1;
  
  const threeRefs = useRef<{
    scene: THREE.Scene | null;
    camera: THREE.PerspectiveCamera | null;
    renderer: THREE.WebGLRenderer | null;
    composer: EffectComposer | null;
    stars: THREE.Points[];
    nebula: THREE.Mesh | null;
    mountains: THREE.Mesh[];
    animationId: number | null;
    locations?: number[];
    targetCameraX?: number;
    targetCameraY?: number;
    targetCameraZ?: number;
  }>({
    scene: null,
    camera: null,
    renderer: null,
    composer: null,
    stars: [],
    nebula: null,
    mountains: [],
    animationId: null
  });

  // Initialize Three.js
  useEffect(() => {
    let isCleanedUp = false;

    // Define all functions using function declarations to avoid hoisting issues
    function createStarField() {
      const { current: refs } = threeRefs;
      if (!refs.scene || isCleanedUp) return;
      
      const starCount = 5000;
      
      for (let i = 0; i < 3; i++) {
        const geometry = new THREE.BufferGeometry();
        const positions = new Float32Array(starCount * 3);
        const colors = new Float32Array(starCount * 3);
        const sizes = new Float32Array(starCount);

        for (let j = 0; j < starCount; j++) {
          const radius = 200 + Math.random() * 800;
          const theta = Math.random() * Math.PI * 2;
          const phi = Math.acos(Math.random() * 2 - 1);

          positions[j * 3] = radius * Math.sin(phi) * Math.cos(theta);
          positions[j * 3 + 1] = radius * Math.sin(phi) * Math.sin(theta);
          positions[j * 3 + 2] = radius * Math.cos(phi);

          const color = new THREE.Color();
          const colorChoice = Math.random();
          if (colorChoice < 0.7) {
            color.setHSL(0, 0, 0.8 + Math.random() * 0.2);
          } else if (colorChoice < 0.9) {
            color.setHSL(0.6, 0.7, 0.8);
          } else {
            color.setHSL(0.08, 0.8, 0.8);
          }
          
          colors[j * 3] = color.r;
          colors[j * 3 + 1] = color.g;
          colors[j * 3 + 2] = color.b;

          sizes[j] = Math.random() * 2 + 0.5;
        }

        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
        geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));

        const material = new THREE.ShaderMaterial({
          uniforms: {
            time: { value: 0 },
            depth: { value: i }
          },
          vertexShader: `
            attribute float size;
            attribute vec3 color;
            varying vec3 vColor;
            uniform float time;
            uniform float depth;
            
            void main() {
              vColor = color;
              vec3 pos = position;
              
              float angle = time * 0.05 * (1.0 - depth * 0.3);
              mat2 rot = mat2(cos(angle), -sin(angle), sin(angle), cos(angle));
              pos.xy = rot * pos.xy;
              
              vec4 mvPosition = modelViewMatrix * vec4(pos, 1.0);
              gl_PointSize = size * (300.0 / -mvPosition.z);
              gl_Position = projectionMatrix * mvPosition;
            }
          `,
          fragmentShader: `
            varying vec3 vColor;
            
            void main() {
              float dist = length(gl_PointCoord - vec2(0.5));
              if (dist > 0.5) discard;
              
              float opacity = 1.0 - smoothstep(0.0, 0.5, dist);
              gl_FragColor = vec4(vColor, opacity);
            }
          `,
          transparent: true,
          blending: THREE.AdditiveBlending,
          depthWrite: false
        });

        const stars = new THREE.Points(geometry, material);
        refs.scene.add(stars);
        refs.stars.push(stars);
      }
    }

    function createNebula() {
      const { current: refs } = threeRefs;
      if (!refs.scene || isCleanedUp) return;
      
      const geometry = new THREE.PlaneGeometry(8000, 4000, 100, 100);
      const material = new THREE.ShaderMaterial({
        uniforms: {
          time: { value: 0 }
        },
        vertexShader: `
          varying vec2 vUv;
          varying vec3 vPosition;
          uniform float time;

          void main() {
            vUv = uv;
            vPosition = position;
            
            vec3 pos = position;
            pos.z += sin(pos.x * 0.01 + time * 0.5) * 10.0;
            pos.z += cos(pos.y * 0.01 + time * 0.3) * 5.0;
            
            gl_Position = projectionMatrix * modelViewMatrix * vec4(pos, 1.0);
          }
        `,
        fragmentShader: `
          varying vec2 vUv;
          varying vec3 vPosition;
          uniform float time;

          void main() {
            vec2 uv = vUv;
            
            float noise = sin(uv.x * 10.0 + time * 0.5) * cos(uv.y * 8.0 + time * 0.3);
            noise += sin(uv.x * 20.0 - time * 0.7) * cos(uv.y * 15.0 - time * 0.4) * 0.5;
            
            vec3 color1 = vec3(0.1, 0.05, 0.3);
            vec3 color2 = vec3(0.3, 0.1, 0.6);
            vec3 color3 = vec3(0.6, 0.2, 0.8);
            
            vec3 finalColor = mix(color1, color2, noise * 0.5 + 0.5);
            finalColor = mix(finalColor, color3, abs(noise) * 0.3);
            
            float alpha = (noise * 0.3 + 0.4) * 0.6;
            gl_FragColor = vec4(finalColor, alpha);
          }
        `,
        transparent: true,
        side: THREE.DoubleSide
      });

      const nebula = new THREE.Mesh(geometry, material);
      nebula.position.z = -800;
      nebula.position.y = 200;
      nebula.rotation.x = 0;
      refs.scene.add(nebula);
      refs.nebula = nebula;
    }

    function createMountains() {
      const { current: refs } = threeRefs;
      if (!refs.scene || isCleanedUp) return;

      const layers = [
        { distance: -50, height: 60, color: 0x1a1a2e, opacity: 1 },
        { distance: -100, height: 80, color: 0x16213e, opacity: 0.8 },
        { distance: -150, height: 100, color: 0x0f3460, opacity: 0.6 },
        { distance: -200, height: 120, color: 0x0a4668, opacity: 0.4 }
      ];

      layers.forEach((layer, index) => {
        const points = [];
        const segments = 50;

        for (let i = 0; i <= segments; i++) {
          const x = (i / segments - 0.5) * 1000;
          const y = Math.sin(i * 0.1) * layer.height +
                   Math.sin(i * 0.05) * layer.height * 0.5 +
                   Math.random() * layer.height * 0.2 - 100;
          points.push(new THREE.Vector2(x, y));
        }

        points.push(new THREE.Vector2(5000, -300));
        points.push(new THREE.Vector2(-5000, -300));

        const shape = new THREE.Shape(points);
        const geometry = new THREE.ShapeGeometry(shape);
        const material = new THREE.MeshBasicMaterial({
          color: layer.color,
          transparent: true,
          opacity: layer.opacity,
          side: THREE.DoubleSide
        });

        const mountain = new THREE.Mesh(geometry, material);
        mountain.position.z = layer.distance;
        mountain.position.y = layer.distance;
        mountain.userData = { baseZ: layer.distance, index };
        refs.scene.add(mountain);
        refs.mountains.push(mountain);
      });
    }

    function createAtmosphere() {
      const { current: refs } = threeRefs;
      if (!refs.scene || isCleanedUp) return;

      const geometry = new THREE.SphereGeometry(600, 32, 32);
      const material = new THREE.ShaderMaterial({
        uniforms: {
          time: { value: 0 }
        },
        vertexShader: `
          varying vec3 vNormal;
          varying vec3 vPosition;

          void main() {
            vNormal = normalize(normalMatrix * normal);
            vPosition = position;
            gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
          }
        `,
        fragmentShader: `
          varying vec3 vNormal;
          varying vec3 vPosition;
          uniform float time;

          void main() {
            float intensity = pow(0.7 - dot(vNormal, vec3(0, 0, 1.0)), 2.0);
            vec3 atmosphere = vec3(0.3, 0.6, 1.0) * intensity;
            gl_FragColor = vec4(atmosphere, intensity * 0.3);
          }
        `,
        transparent: true,
        side: THREE.BackSide
      });

      const atmosphere = new THREE.Mesh(geometry, material);
      refs.scene.add(atmosphere);
    }

    function getLocation() {
      const { current: refs } = threeRefs;
      const locations: number[] = [];
      refs.mountains.forEach((mountain, i) => {
        locations[i] = mountain.position.z;
      });
      refs.locations = locations;
    }

    function animate() {
      const { current: refs } = threeRefs;
      if (isCleanedUp) return;
      
      refs.animationId = requestAnimationFrame(animate);

      const time = Date.now() * 0.001;

      // Update stars
      refs.stars.forEach((starField) => {
        if (starField.material && 'uniforms' in starField.material) {
          (starField.material as THREE.ShaderMaterial).uniforms.time.value = time;
        }
      });

      // Update nebula
      if (refs.nebula && refs.nebula.material && 'uniforms' in refs.nebula.material) {
        (refs.nebula.material as THREE.ShaderMaterial).uniforms.time.value = time * 0.5;
      }

      // Parallax mountains with subtle animation
      refs.mountains.forEach((mountain, i) => {
        const parallaxFactor = 1 + i * 0.5;
        mountain.position.x = Math.sin(time * 0.1) * 2 * parallaxFactor;
        mountain.position.y = 50 + (Math.cos(time * 0.15) * 1 * parallaxFactor);
      });

      if (refs.composer) {
        refs.composer.render();
      }
    }

    function initThree() {
      const { current: refs } = threeRefs;

      // Scene setup
      refs.scene = new THREE.Scene();
      refs.scene.fog = new THREE.FogExp2(0x000000, 0.00025);

      // Camera
      refs.camera = new THREE.PerspectiveCamera(
        75,
        window.innerWidth / window.innerHeight,
        0.1,
        2000
      );
      refs.camera.position.z = 100;
      refs.camera.position.y = 20;

      // Renderer
      if (canvasRef.current) {
        refs.renderer = new THREE.WebGLRenderer({
          canvas: canvasRef.current,
          antialias: true,
          alpha: true
        });
        refs.renderer.setSize(window.innerWidth, window.innerHeight);
        refs.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        refs.renderer.toneMapping = THREE.ACESFilmicToneMapping;
        refs.renderer.toneMappingExposure = 0.5;

        // Post-processing
        refs.composer = new EffectComposer(refs.renderer);
        const renderPass = new RenderPass(refs.scene, refs.camera);
        refs.composer.addPass(renderPass);

        const bloomPass = new UnrealBloomPass(
          new THREE.Vector2(window.innerWidth, window.innerHeight),
          0.8,
          0.4,
          0.85
        );
        refs.composer.addPass(bloomPass);
      }

      // Create scene elements
      createStarField();
      createNebula();
      createMountains();
      createAtmosphere();
      getLocation();

      // Start animation
      animate();

      // Mark as ready after Three.js is initialized
      setIsReady(true);
    }

    // Handle resize
    function handleResize() {
      const { current: refs } = threeRefs;
      if (refs.camera && refs.renderer && refs.composer) {
        refs.camera.aspect = window.innerWidth / window.innerHeight;
        refs.camera.updateProjectionMatrix();
        refs.renderer.setSize(window.innerWidth, window.innerHeight);
        refs.composer.setSize(window.innerWidth, window.innerHeight);
      }
    }

    // Initialize Three.js
    try {
      initThree();
    } catch (error) {
      console.error('Failed to initialize Three.js:', error);
      setIsReady(true); // Still show the page even if 3D fails
    }

    window.addEventListener('resize', handleResize);

    // Cleanup function
    return () => {
      isCleanedUp = true;
      const { current: refs } = threeRefs;

      window.removeEventListener('resize', handleResize);

      if (refs.animationId) {
        cancelAnimationFrame(refs.animationId);
      }

      // Dispose of Three.js resources
      refs.stars.forEach(starField => {
        if (starField.geometry) starField.geometry.dispose();
        if (starField.material) {
          if (Array.isArray(starField.material)) {
            starField.material.forEach(mat => mat.dispose());
          } else {
            starField.material.dispose();
          }
        }
      });

      refs.mountains.forEach(mountain => {
        if (mountain.geometry) mountain.geometry.dispose();
        if (mountain.material) {
          if (Array.isArray(mountain.material)) {
            mountain.material.forEach(mat => mat.dispose());
          } else {
            mountain.material.dispose();
          }
        }
      });

      if (refs.nebula) {
        if (refs.nebula.geometry) refs.nebula.geometry.dispose();
        if (refs.nebula.material) {
          if (Array.isArray(refs.nebula.material)) {
            refs.nebula.material.forEach(mat => mat.dispose());
          } else {
            refs.nebula.material.dispose();
          }
        }
      }

      if (refs.renderer) {
        refs.renderer.dispose();
      }
    };
  }, []);

  // GSAP Animations - Run after component is ready
  useEffect(() => {
    if (!isReady) return;

    const tl = gsap.timeline();

    // Animate title
    if (titleRef.current) {
      gsap.set(titleRef.current, { opacity: 0, y: 50 });
      tl.to(titleRef.current, {
        opacity: 1,
        y: 0,
        duration: 1.5,
        ease: "power3.out"
      });
    }

    // Animate subtitle
    if (subtitleRef.current) {
      gsap.set(subtitleRef.current, { opacity: 0, y: 30 });
      tl.to(subtitleRef.current, {
        opacity: 1,
        y: 0,
        duration: 1.2,
        ease: "power3.out"
      }, "-=1");
    }

    return () => {
      tl.kill();
    };
  }, [isReady]);

  return (
    <div className="relative min-h-screen bg-black overflow-hidden" ref={containerRef}>
      {/* Three.js Canvas */}
      <canvas
        ref={canvasRef}
        className="fixed inset-0 w-full h-full"
        style={{ zIndex: 1 }}
      />

      {/* Content Overlay */}
      <div className="relative z-10 min-h-screen flex flex-col">
        {/* Hero Section */}
        <section className="flex-1 flex items-center justify-center px-4 py-20">
          <div className="text-center max-w-4xl mx-auto">
            <h1
              ref={titleRef}
              className="text-6xl md:text-8xl font-bold text-white mb-8 leading-tight"
              style={{
                background: 'linear-gradient(135deg, #ffffff 0%, #3b82f6 50%, #f59e0b 100%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text'
              }}
            >
              Hive Campus
            </h1>

            <div
              ref={subtitleRef}
              className="text-xl md:text-2xl text-gray-300 mb-12 max-w-2xl mx-auto leading-relaxed"
            >
              The student-exclusive marketplace where campus commerce comes alive.
              Buy, sell, and connect with your university community.
            </div>

            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
              <Link to="/signup">
                <AnimatedButton className="px-8 py-4 text-lg font-semibold">
                  Get Started
                </AnimatedButton>
              </Link>

              <Link
                to="/login"
                className="px-8 py-4 text-lg font-semibold text-white border-2 border-white/20 rounded-lg hover:border-white/40 transition-all duration-300 backdrop-blur-sm"
              >
                Sign In
              </Link>
            </div>
          </div>
        </section>

        {/* Footer */}
        <footer className="relative z-10 py-8 text-center text-gray-400">
          <p>&copy; 2024 Hive Campus. All rights reserved.</p>
        </footer>
      </div>
    </div>
  );
};

export default HiveCampusLanding;
