import { useState, useEffect } from 'react';
import { Listing } from '../firebase/types';
import { useListings } from './useListings';
import { useAuth } from './useAuth';

interface SuggestedListingsOptions {
  currentListing: Listing | null;
  maxSuggestions?: number;
}

export const useSuggestedListings = ({
  currentListing,
  maxSuggestions = 6
}: SuggestedListingsOptions) => {
  const [suggestedListings, setSuggestedListings] = useState<Listing[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { listings, fetchListings } = useListings();
  const { currentUser } = useAuth();

  useEffect(() => {
    if (!currentListing) {
      setSuggestedListings([]);
      return;
    }

    // If we don't have listings data, fetch it
    if (!listings || listings.length === 0) {
      fetchListings({}, 50); // Fetch up to 50 listings for suggestions
      return;
    }

    setIsLoading(true);

    try {
      // AI-based suggestion algorithm
      const suggestions = generateSuggestions(currentListing, listings, currentUser?.uid);
      setSuggestedListings(suggestions.slice(0, maxSuggestions));
    } catch (error) {
      console.error('Error generating suggestions:', error);
      setSuggestedListings([]);
    } finally {
      setIsLoading(false);
    }
  }, [currentListing, listings, currentUser?.uid, maxSuggestions, fetchListings]);

  return { suggestedListings, isLoading };
};

// AI-based suggestion algorithm
function generateSuggestions(
  currentListing: Listing,
  allListings: Listing[],
  currentUserId?: string
): Listing[] {
  // Filter out the current listing and user's own listings
  const candidateListings = allListings.filter(listing =>
    listing.id !== currentListing.id &&
    listing.ownerId !== currentUserId &&
    listing.ownerId !== currentListing.ownerId &&
    listing.status === 'active'
  );

  if (candidateListings.length === 0) {
    return [];
  }

  // Score each listing based on multiple factors
  const scoredListings = candidateListings.map(listing => ({
    listing,
    score: calculateSimilarityScore(currentListing, listing)
  }));

  // Sort by score (highest first) and return the listings
  return scoredListings
    .sort((a, b) => b.score - a.score)
    .map(item => item.listing);
}

// Calculate similarity score between two listings
function calculateSimilarityScore(current: Listing, candidate: Listing): number {
  let score = 0;

  // Category similarity (highest weight)
  if (current.category === candidate.category) {
    score += 40;
  }

  // Price similarity (within reasonable range)
  const priceDiff = Math.abs(current.price - candidate.price);
  const avgPrice = (current.price + candidate.price) / 2;
  const priceRatio = priceDiff / avgPrice;
  
  if (priceRatio <= 0.2) score += 25; // Within 20%
  else if (priceRatio <= 0.5) score += 15; // Within 50%
  else if (priceRatio <= 1.0) score += 5; // Within 100%

  // Condition similarity
  const conditionScore = getConditionScore(current.condition, candidate.condition);
  score += conditionScore;

  // University similarity (prefer same university)
  if (current.university === candidate.university) {
    score += 15;
  }

  // Type similarity (sell, rent, auction)
  if (current.type === candidate.type) {
    score += 10;
  }

  // Title/description keyword similarity
  const keywordScore = calculateKeywordSimilarity(current, candidate);
  score += keywordScore;

  // Recency bonus (newer listings get slight preference)
  const daysSinceCreated = getDaysSinceCreated(candidate.createdAt);
  if (daysSinceCreated <= 7) score += 5;
  else if (daysSinceCreated <= 30) score += 2;

  return score;
}

// Calculate condition similarity score
function getConditionScore(condition1: string, condition2: string): number {
  const conditionRanking = {
    'new': 5,
    'like_new': 4,
    'very_good': 3,
    'good': 2,
    'fair': 1,
    'poor': 0
  };

  const rank1 = conditionRanking[condition1 as keyof typeof conditionRanking] || 0;
  const rank2 = conditionRanking[condition2 as keyof typeof conditionRanking] || 0;
  
  const diff = Math.abs(rank1 - rank2);
  
  if (diff === 0) return 10; // Same condition
  if (diff === 1) return 7;  // Adjacent conditions
  if (diff === 2) return 4;  // Close conditions
  return 1; // Different conditions
}

// Calculate keyword similarity between titles and descriptions
function calculateKeywordSimilarity(current: Listing, candidate: Listing): number {
  const currentText = `${current.title} ${current.description}`.toLowerCase();
  const candidateText = `${candidate.title} ${candidate.description}`.toLowerCase();
  
  // Extract meaningful keywords (filter out common words)
  const stopWords = new Set(['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those']);
  
  const currentKeywords = currentText
    .split(/\s+/)
    .filter(word => word.length > 2 && !stopWords.has(word))
    .slice(0, 10); // Limit to top 10 keywords
  
  const candidateKeywords = new Set(
    candidateText
      .split(/\s+/)
      .filter(word => word.length > 2 && !stopWords.has(word))
  );
  
  const matchingKeywords = currentKeywords.filter(keyword => 
    candidateKeywords.has(keyword)
  );
  
  // Score based on percentage of matching keywords
  const matchRatio = matchingKeywords.length / Math.max(currentKeywords.length, 1);
  return Math.round(matchRatio * 15); // Max 15 points for keyword similarity
}

// Get days since listing was created
function getDaysSinceCreated(createdAt: any): number {
  try {
    let date: Date;
    
    if (createdAt?.toDate && typeof createdAt.toDate === 'function') {
      date = createdAt.toDate();
    } else if (createdAt?.seconds) {
      date = new Date(createdAt.seconds * 1000);
    } else {
      date = new Date(createdAt);
    }
    
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  } catch (error) {
    return 999; // Return high number if date parsing fails
  }
}
