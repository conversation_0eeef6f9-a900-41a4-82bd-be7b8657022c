# Hive Campus Design System

## Overview
This design system defines the visual language and UI components for Hive Campus, a student-exclusive marketplace app. The design follows a liquid glass aesthetic with blur effects and modern UI principles, optimized for mobile-first experiences.

## Core Design Principles

1. **Liquid Glass Aesthetic**: Translucent surfaces with blur effects that create depth and hierarchy
2. **Vibrant yet Trustworthy**: Bold colors balanced with clean, professional layouts
3. **Mobile-First**: Optimized for touch interactions and smaller screens
4. **Accessible**: High contrast, readable text, and clear interactive elements
5. **Gen Z Appeal**: Modern, trendy design elements that resonate with college students

## Color Palette

### Primary Colors
- **Primary Blue**: `#3b82f6` (Primary-500)
  - Light shades: `#eff6ff`, `#dbeafe`, `#bfdbfe`, `#93c5fd`, `#60a5fa`
  - Dark shades: `#2563eb`, `#1d4ed8`, `#1e40af`, `#1e3a8a`

### Accent Colors
- **Accent Orange**: `#f97316` (Accent-500)
  - Light shades: `#fff7ed`, `#ffedd5`, `#fed7aa`, `#fdba74`, `#fb923c`
  - Dark shades: `#ea580c`, `#c2410c`, `#9a3412`, `#7c2d12`

### Success Colors
- **Success Green**: `#10b981` (Success-500)
  - Light shades: `#ecfdf5`, `#d1fae5`, `#a7f3d0`, `#6ee7b7`, `#34d399`
  - Dark shades: `#059669`, `#047857`, `#065f46`, `#064e3b`

### Additional Colors
- **Warning**: `#fbbf24` (Amber-400)
- **Error**: `#ef4444` (Red-500)
- **Info**: `#3b82f6` (Blue-500)
- **Neutral**: 
  - Light mode: White backgrounds with gray text (`#1f2937`)
  - Dark mode: Dark backgrounds (`#111827`) with light text (`#f9fafb`)

## Typography

### Font Family
- **Primary Font**: 'Inter', sans-serif
- **Fallback**: system-ui, sans-serif

### Font Sizes
- **Headings**:
  - H1: 2.5rem (40px) / 3rem (48px) on desktop
  - H2: 2rem (32px)
  - H3: 1.5rem (24px)
  - H4: 1.25rem (20px)
  - H5: 1.125rem (18px)
  - H6: 1rem (16px)

- **Body Text**:
  - Large: 1.125rem (18px)
  - Regular: 1rem (16px)
  - Small: 0.875rem (14px)
  - XSmall: 0.75rem (12px)

### Font Weights
- **Regular**: 400
- **Medium**: 500
- **Semibold**: 600
- **Bold**: 700

## Spacing System

- **4px base unit**
- **Spacing scale**: 4px, 8px, 12px, 16px, 24px, 32px, 48px, 64px, 96px, 128px

## Border Radius

- **Small**: 6px
- **Medium**: 12px
- **Large**: 16px
- **XLarge**: 24px
- **Pill**: 9999px (for badges and tags)

## Shadows

### Light Mode
- **Glass Effect**: `background: rgba(255, 255, 255, 0.7); backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.2);`
- **Subtle**: `0 1px 2px rgba(0, 0, 0, 0.05)`
- **Medium**: `0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)`
- **Large**: `0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)`
- **XLarge**: `0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)`

### Dark Mode
- **Glass Effect**: `background: rgba(17, 24, 39, 0.7); backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.08);`
- **Subtle**: `0 1px 2px rgba(0, 0, 0, 0.3)`
- **Medium**: `0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2)`
- **Large**: `0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2)`
- **XLarge**: `0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2)`

## Animations

- **Fade In**: `fadeIn 0.5s ease-in-out`
- **Slide Up**: `slideUp 0.3s ease-out`
- **Scale In**: `scaleIn 0.2s ease-out`
- **Bounce Gentle**: `bounce 2s infinite`

## Core Components

### Buttons

#### Primary Button
```jsx
<button className="bg-primary-600 hover:bg-primary-700 text-white py-3 px-6 rounded-xl font-semibold transition-all duration-200 flex items-center justify-center space-x-2 shadow-md hover:shadow-lg transform hover:translate-y-[-2px]">
  <Icon className="w-5 h-5" />
  <span>Button Text</span>
</button>
```

#### Secondary Button
```jsx
<button className="bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 py-3 px-6 rounded-xl font-semibold transition-all duration-200 flex items-center justify-center space-x-2 shadow-sm hover:shadow-md">
  <Icon className="w-5 h-5" />
  <span>Button Text</span>
</button>
```

#### Glass Button
```jsx
<button className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-md border border-white/20 dark:border-white/10 text-gray-900 dark:text-white py-3 px-6 rounded-xl font-semibold transition-all duration-200 flex items-center justify-center space-x-2 shadow-sm hover:shadow-md hover:bg-white/80 dark:hover:bg-gray-800/80">
  <Icon className="w-5 h-5" />
  <span>Button Text</span>
</button>
```

### Cards

#### Standard Card
```jsx
<div className="bg-white dark:bg-gray-800 rounded-2xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden">
  <div className="p-6">
    <h3 className="font-bold text-lg text-gray-900 dark:text-white mb-2">Card Title</h3>
    <p className="text-gray-600 dark:text-gray-400">Card content goes here</p>
  </div>
</div>
```

#### Glass Card
```jsx
<div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-md border border-white/20 dark:border-white/10 rounded-2xl shadow-lg overflow-hidden">
  <div className="p-6">
    <h3 className="font-bold text-lg text-gray-900 dark:text-white mb-2">Glass Card Title</h3>
    <p className="text-gray-600 dark:text-gray-400">Card content with glass effect</p>
  </div>
</div>
```

#### Product Card
```jsx
<div className="bg-white dark:bg-gray-800 rounded-2xl shadow-md hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02] overflow-hidden group">
  <div className="relative overflow-hidden">
    <img
      src="product-image.jpg"
      alt="Product Title"
      className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-300"
    />
    <div className="absolute top-4 left-4">
      <span className="bg-success-500 text-white px-3 py-1 rounded-full text-sm font-medium">
        20% OFF
      </span>
    </div>
  </div>
  <div className="p-6">
    <h3 className="font-bold text-lg text-gray-900 dark:text-white mb-2 line-clamp-2">
      Product Title
    </h3>
    <div className="flex items-center space-x-2 mb-3">
      <span className="text-2xl font-bold text-primary-600 dark:text-primary-400">
        $99
      </span>
      <span className="text-sm text-gray-500 dark:text-gray-400 line-through">
        $129
      </span>
    </div>
    <div className="flex space-x-3">
      <button className="flex-1 bg-primary-600 hover:bg-primary-700 text-white py-3 rounded-xl font-semibold transition-colors flex items-center justify-center space-x-2">
        <ShoppingCart className="w-4 h-4" />
        <span>Buy Now</span>
      </button>
      <button className="flex-1 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 py-3 rounded-xl font-semibold transition-colors flex items-center justify-center space-x-2">
        <MessageCircle className="w-4 h-4" />
        <span>Chat</span>
      </button>
    </div>
  </div>
</div>
```

### Form Elements

#### Text Input
```jsx
<div className="mb-4">
  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
    Label Text
  </label>
  <div className="relative">
    <input
      type="text"
      className="w-full px-4 py-3 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200"
      placeholder="Placeholder text"
    />
    <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
      <Icon className="h-5 w-5 text-gray-400" />
    </div>
  </div>
  <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">Helper text goes here</p>
</div>
```

#### Glass Input
```jsx
<div className="mb-4">
  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
    Label Text
  </label>
  <div className="relative">
    <input
      type="text"
      className="w-full px-4 py-3 bg-white/70 dark:bg-gray-800/70 backdrop-blur-md border border-white/20 dark:border-white/10 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200"
      placeholder="Placeholder text"
    />
    <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
      <Icon className="h-5 w-5 text-gray-400" />
    </div>
  </div>
</div>
```

#### Search Input
```jsx
<div className="relative">
  <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
  <input
    type="text"
    placeholder="Search for items..."
    className="w-full pl-12 pr-4 py-3 bg-white/70 dark:bg-gray-800/70 backdrop-blur-md border border-white/20 dark:border-white/10 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent"
  />
</div>
```

### Navigation

#### Bottom Navigation
```jsx
<nav className="fixed bottom-0 left-0 right-0 z-40 bg-white/95 dark:bg-gray-800/95 backdrop-blur-md border-t border-gray-200 dark:border-gray-700 md:hidden safe-area-inset-bottom">
  <div className="flex justify-around py-2">
    {navItems.map((item) => {
      const Icon = item.icon;
      const isActive = location.pathname === item.path;
      
      return (
        <Link
          key={item.path}
          to={item.path}
          className={`flex flex-col items-center p-2 relative ${
            isActive 
              ? 'text-primary-600 dark:text-primary-400' 
              : 'text-gray-600 dark:text-gray-400'
          }`}
        >
          <Icon className="w-6 h-6" />
          <span className="text-xs mt-1">{item.label}</span>
          {item.hasNotification && (
            <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
          )}
        </Link>
      );
    })}
  </div>
</nav>
```

#### Glass Header
```jsx
<header className="fixed top-0 left-0 right-0 z-50 bg-white/95 dark:bg-gray-800/95 backdrop-blur-md border-b border-gray-200 dark:border-gray-700">
  <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div className="flex justify-between items-center h-16">
      <Link to="/" className="flex items-center space-x-2">
        <img
          src="/hive-campus-logo.svg"
          alt="Hive Campus Logo"
          className="w-8 h-8"
        />
        <span className="text-xl font-bold text-gray-900 dark:text-white">Hive Campus</span>
      </Link>
      
      <div className="flex items-center">
        <UserProfileDropdown user={user} />
      </div>
    </div>
  </div>
</header>
```

## Micro-interactions

### Button Hover
- Scale up slightly: `transform hover:scale-105`
- Subtle shadow increase: `shadow-md hover:shadow-lg`
- Background color darkening: `bg-primary-600 hover:bg-primary-700`

### Card Hover
- Shadow increase: `shadow-md hover:shadow-xl`
- Slight scale: `transform hover:scale-[1.02]`
- Image zoom: `group-hover:scale-110 transition-transform duration-300`

### Input Focus
- Ring effect: `focus:ring-2 focus:ring-primary-500`
- Border transparency: `focus:border-transparent`

### Loading States
- Skeleton loading with pulse effect
- Spinner animation for buttons and page loads

## Responsive Breakpoints

- **sm**: 640px
- **md**: 768px
- **lg**: 1024px
- **xl**: 1280px
- **2xl**: 1536px

## Accessibility Guidelines

- Maintain color contrast ratio of at least 4.5:1 for normal text
- Ensure interactive elements have clear focus states
- Provide text alternatives for non-text content
- Design for keyboard navigation
- Support screen readers with proper ARIA attributes

## Dark Mode Support

All components should have dark mode variants using the `dark:` prefix in Tailwind classes. The app uses the `class` strategy for dark mode, toggled with a class on the `html` element.