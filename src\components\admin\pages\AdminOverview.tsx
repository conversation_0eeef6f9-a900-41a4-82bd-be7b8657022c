import React, { useState, useEffect } from 'react';
import {
  Users,
  Package,
  DollarSign,
  TrendingUp,
  AlertTriangle,
  Eye,
  ShoppingCart,
  MessageSquare,
  Truck,
  RefreshCw,
  Activity,
  Shield,
  Clock,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { AdminDataService, AdminMetrics } from '../../../services/AdminDataService';
import { AdminStripeService, StripeMetrics } from '../../../services/adminStripeService';
import { AdminShippoService, ShippingMetrics } from '../../../services/adminShippoService';
import { AdminSentryService, SentryMetrics } from '../../../services/adminSentryService';
import { useAdminData } from '../../../hooks/useAdminData';
import NotificationTest from '../NotificationTest';
import NotificationVerifier from '../NotificationVerifier';

interface MetricCardProps {
  title: string;
  value: string | number;
  change?: string;
  changeType?: 'positive' | 'negative' | 'neutral';
  icon: React.ComponentType<{ className?: string }>;
}

const MetricCard: React.FC<MetricCardProps> = ({ 
  title, 
  value, 
  change, 
  changeType = 'neutral', 
  icon: Icon 
}) => {
  const changeColor = {
    positive: 'text-green-600 dark:text-green-400',
    negative: 'text-red-600 dark:text-red-400',
    neutral: 'text-gray-600 dark:text-gray-400'
  }[changeType];

  return (
    <div className="bg-white dark:bg-gray-800 overflow-hidden shadow-sm rounded-lg border border-gray-200 dark:border-gray-700">
      <div className="p-5">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <Icon className="h-6 w-6 text-gray-400" />
          </div>
          <div className="ml-5 w-0 flex-1">
            <dl>
              <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                {title}
              </dt>
              <dd className="flex items-baseline">
                <div className="text-2xl font-semibold text-gray-900 dark:text-white">
                  {value}
                </div>
                {change && (
                  <div className={`ml-2 flex items-baseline text-sm font-semibold ${changeColor}`}>
                    {change}
                  </div>
                )}
              </dd>
            </dl>
          </div>
        </div>
      </div>
    </div>
  );
};

const AdminOverview: React.FC = () => {
  const { dashboardStats, isLoading, isRefreshing, error, refreshStats } = useAdminData();
  const [adminMetrics, setAdminMetrics] = useState<AdminMetrics | null>(null);
  const [stripeMetrics, setStripeMetrics] = useState<StripeMetrics | null>(null);
  const [shippingMetrics, setShippingMetrics] = useState<ShippingMetrics | null>(null);
  const [sentryMetrics, setSentryMetrics] = useState<SentryMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [metricsError, setMetricsError] = useState<string | null>(null);

  useEffect(() => {
    const fetchAllMetrics = async () => {
      try {
        setLoading(true);
        setMetricsError(null);

        // Fetch all metrics in parallel
        const [admin, stripe, shipping, sentry] = await Promise.allSettled([
          AdminDataService.getMetrics(),
          AdminStripeService.getStripeMetrics(),
          AdminShippoService.getShippingMetrics(),
          AdminSentryService.getSentryMetrics()
        ]);

        // Handle admin metrics
        if (admin.status === 'fulfilled') {
          setAdminMetrics(admin.value);
        } else {
          console.error('Error fetching admin metrics:', admin.reason);
        }

        // Handle Stripe metrics
        if (stripe.status === 'fulfilled') {
          setStripeMetrics(stripe.value);
        } else {
          console.error('Error fetching Stripe metrics:', stripe.reason);
        }

        // Handle shipping metrics
        if (shipping.status === 'fulfilled') {
          setShippingMetrics(shipping.value);
        } else {
          console.error('Error fetching shipping metrics:', shipping.reason);
        }

        // Handle Sentry metrics
        if (sentry.status === 'fulfilled') {
          setSentryMetrics(sentry.value);
        } else {
          console.error('Error fetching Sentry metrics:', sentry.reason);
        }

      } catch (err) {
        console.error('Error fetching metrics:', err);
        setMetricsError('Failed to load dashboard metrics');
      } finally {
        setLoading(false);
      }
    };

    fetchAllMetrics();

    // Set up real-time updates for admin metrics
    const unsubscribe = AdminDataService.subscribeToMetrics((newMetrics) => {
      setAdminMetrics(newMetrics);
    });

    return () => {
      unsubscribe();
    };
  }, []);

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="bg-white dark:bg-gray-800 overflow-hidden shadow-sm rounded-lg border border-gray-200 dark:border-gray-700">
              <div className="p-5">
                <div className="animate-pulse">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="h-6 w-6 bg-gray-300 dark:bg-gray-600 rounded"></div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                      <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-3/4 mb-2"></div>
                      <div className="h-6 bg-gray-300 dark:bg-gray-600 rounded w-1/2"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex">
            <AlertTriangle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                Error Loading Dashboard
              </h3>
              <p className="mt-1 text-sm text-red-700 dark:text-red-300">
                {error}
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Refresh Button */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Admin Dashboard</h1>
          <p className="text-gray-600 dark:text-gray-400">
            Platform overview and key metrics
          </p>
        </div>
        <button
          onClick={refreshStats}
          disabled={isRefreshing}
          className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 disabled:opacity-50"
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
          {isRefreshing ? 'Refreshing...' : 'Refresh'}
        </button>
      </div>

      {/* Enhanced Metrics Grid */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6">
        <MetricCard
          title="Total Users"
          value={dashboardStats?.users?.total?.toLocaleString() || adminMetrics?.totalUsers?.toLocaleString() || '0'}
          change={`${dashboardStats?.users?.active || adminMetrics?.activeUsers || 0} active`}
          changeType="neutral"
          icon={Users}
        />
        <MetricCard
          title="Active Listings"
          value={dashboardStats?.listings?.active || adminMetrics?.activeListings || 0}
          change={`${dashboardStats?.listings?.total || adminMetrics?.totalListings || 0} total`}
          changeType="neutral"
          icon={Package}
        />
        <MetricCard
          title="Total Revenue"
          value={`$${dashboardStats?.orders?.revenue?.toLocaleString() || stripeMetrics?.totalRevenue?.toLocaleString() || '0'}`}
          change={`${dashboardStats?.orders?.completed || stripeMetrics?.successfulTransactions || 0} orders`}
          changeType="positive"
          icon={DollarSign}
        />
        <MetricCard
          title="Wallet Balance"
          value={`$${dashboardStats?.wallet?.totalBalance?.toLocaleString() || '0'}`}
          change={`${dashboardStats?.wallet?.activeWallets || 0} active`}
          changeType="neutral"
          icon={ShoppingCart}
        />
        <MetricCard
          title="Active Shipments"
          value={shippingMetrics?.activeShipments || 0}
          change={`${shippingMetrics?.deliveredShipments || 0} delivered`}
          changeType="positive"
          icon={Truck}
        />
        <MetricCard
          title="Pending Reports"
          value={dashboardStats?.reports?.pending || adminMetrics?.pendingReports || 0}
          change={dashboardStats?.reports?.pending ? "Needs attention" : "All clear"}
          changeType={dashboardStats?.reports?.pending ? "negative" : "positive"}
          icon={AlertTriangle}
        />
      </div>

      {/* Quick Actions */}
      <div className="bg-white dark:bg-gray-800 shadow-sm rounded-lg border border-gray-200 dark:border-gray-700">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4">
            Quick Actions
          </h3>
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
            <button className="flex items-center p-3 text-left border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
              <Eye className="h-5 w-5 text-blue-500 mr-3" />
              <div>
                <p className="text-sm font-medium text-gray-900 dark:text-white">View Users</p>
                <p className="text-xs text-gray-500 dark:text-gray-400">Manage user accounts</p>
              </div>
            </button>
            <button className="flex items-center p-3 text-left border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
              <ShoppingCart className="h-5 w-5 text-green-500 mr-3" />
              <div>
                <p className="text-sm font-medium text-gray-900 dark:text-white">Review Listings</p>
                <p className="text-xs text-gray-500 dark:text-gray-400">Moderate content</p>
              </div>
            </button>
            <button className="flex items-center p-3 text-left border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
              <MessageSquare className="h-5 w-5 text-purple-500 mr-3" />
              <div>
                <p className="text-sm font-medium text-gray-900 dark:text-white">Monitor Chat</p>
                <p className="text-xs text-gray-500 dark:text-gray-400">Review messages</p>
              </div>
            </button>
            <button className="flex items-center p-3 text-left border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
              <TrendingUp className="h-5 w-5 text-orange-500 mr-3" />
              <div>
                <p className="text-sm font-medium text-gray-900 dark:text-white">View Analytics</p>
                <p className="text-xs text-gray-500 dark:text-gray-400">Platform insights</p>
              </div>
            </button>
          </div>
        </div>
      </div>

      {/* System Status & Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* System Status */}
        <div className="bg-white dark:bg-gray-800 shadow-sm rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4">
              System Status
            </h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="flex-shrink-0 w-3 h-3 bg-green-400 rounded-full"></div>
                  <span className="ml-3 text-sm text-gray-600 dark:text-gray-300">Firebase</span>
                </div>
                <span className="text-sm text-green-600 dark:text-green-400">Operational</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="flex-shrink-0 w-3 h-3 bg-green-400 rounded-full"></div>
                  <span className="ml-3 text-sm text-gray-600 dark:text-gray-300">Stripe</span>
                </div>
                <span className="text-sm text-green-600 dark:text-green-400">Operational</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="flex-shrink-0 w-3 h-3 bg-green-400 rounded-full"></div>
                  <span className="ml-3 text-sm text-gray-600 dark:text-gray-300">Shippo</span>
                </div>
                <span className="text-sm text-green-600 dark:text-green-400">Operational</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="flex-shrink-0 w-3 h-3 bg-yellow-400 rounded-full"></div>
                  <span className="ml-3 text-sm text-gray-600 dark:text-gray-300">Sentry</span>
                </div>
                <span className="text-sm text-yellow-600 dark:text-yellow-400">
                  {sentryMetrics?.newErrors || 0} new errors
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-white dark:bg-gray-800 shadow-sm rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4">
              Recent Activity
            </h3>
            <div className="space-y-3">
              <div className="flex items-center text-sm">
                <div className="flex-shrink-0 w-2 h-2 bg-green-400 rounded-full"></div>
                <p className="ml-3 text-gray-600 dark:text-gray-300">
                  {adminMetrics?.totalUsers || 0} users registered
                </p>
                <span className="ml-auto text-gray-400 text-xs">Today</span>
              </div>
              <div className="flex items-center text-sm">
                <div className="flex-shrink-0 w-2 h-2 bg-blue-400 rounded-full"></div>
                <p className="ml-3 text-gray-600 dark:text-gray-300">
                  {adminMetrics?.activeListings || 0} active listings
                </p>
                <span className="ml-auto text-gray-400 text-xs">Current</span>
              </div>
              <div className="flex items-center text-sm">
                <div className="flex-shrink-0 w-2 h-2 bg-purple-400 rounded-full"></div>
                <p className="ml-3 text-gray-600 dark:text-gray-300">
                  {stripeMetrics?.successfulTransactions || 0} transactions completed
                </p>
                <span className="ml-auto text-gray-400 text-xs">Today</span>
              </div>
              <div className="flex items-center text-sm">
                <div className="flex-shrink-0 w-2 h-2 bg-orange-400 rounded-full"></div>
                <p className="ml-3 text-gray-600 dark:text-gray-300">
                  {shippingMetrics?.activeShipments || 0} packages in transit
                </p>
                <span className="ml-auto text-gray-400 text-xs">Current</span>
              </div>
              {sentryMetrics?.newErrors && sentryMetrics.newErrors > 0 && (
                <div className="flex items-center text-sm">
                  <div className="flex-shrink-0 w-2 h-2 bg-red-400 rounded-full"></div>
                  <p className="ml-3 text-gray-600 dark:text-gray-300">
                    {sentryMetrics.newErrors} new errors detected
                  </p>
                  <span className="ml-auto text-gray-400 text-xs">Recent</span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Notification System Test */}
        <div className="col-span-1 lg:col-span-2">
          <NotificationTest />
        </div>

        {/* Notification Verifier */}
        <div className="col-span-1 lg:col-span-2">
          <NotificationVerifier />
        </div>
      </div>
    </div>
  );
};

export default AdminOverview;
