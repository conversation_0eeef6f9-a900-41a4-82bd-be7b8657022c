import React, { useState, useEffect } from 'react';
import {
  DollarSign,
  Clock,
  CheckCircle,
  AlertTriangle,
  RefreshCw,
  Eye,
  User,
  Package,
  Calendar
} from 'lucide-react';
import { AdminStripeService } from '../../services/adminStripeService';
import { useAuth } from '../../hooks/useAuth';
import { logAdminAction } from '../../utils/adminAuth';
import toast from 'react-hot-toast';

interface PendingPayout {
  id: string;
  sellerId: string;
  sellerName: string;
  buyerId: string;
  buyerName: string;
  listingTitle: string;
  totalAmount: number;
  platformFee: number;
  sellerAmount: number;
  createdAt: any;
  status: string;
  secretCode?: string;
}

const PayoutManagement: React.FC = () => {
  const { userProfile } = useAuth();
  const [pendingPayouts, setPendingPayouts] = useState<PendingPayout[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [processingPayouts, setProcessingPayouts] = useState<Set<string>>(new Set());

  useEffect(() => {
    fetchPendingPayouts();
  }, []);

  const fetchPendingPayouts = async () => {
    try {
      setLoading(true);
      setError(null);
      const payouts = await AdminStripeService.getPendingPayouts();
      
      // Transform the data to include calculated amounts
      const transformedPayouts = payouts.map(payout => {
        const platformFee = (payout.totalAmount || 0) * (payout.isTextbook ? 0.08 : 0.10);
        const sellerAmount = (payout.totalAmount || 0) - platformFee;
        
        return {
          ...payout,
          platformFee,
          sellerAmount
        };
      });
      
      setPendingPayouts(transformedPayouts);
    } catch (err) {
      console.error('Error fetching pending payouts:', err);
      setError('Failed to load pending payouts');
    } finally {
      setLoading(false);
    }
  };

  const handleReleasePayout = async (payoutId: string) => {
    if (!confirm('⚠️ Are you sure you want to release this payout? This action cannot be undone.')) {
      return;
    }

    try {
      setProcessingPayouts(prev => new Set(prev).add(payoutId));
      
      if (userProfile) {
        await logAdminAction(userProfile, 'payout_release', { payoutId });
      }

      await AdminStripeService.forceReleaseFunds(payoutId, 'Admin manual payout release');
      
      toast.success('✅ Payout released successfully!');
      await fetchPendingPayouts(); // Refresh the list
      
    } catch (err) {
      console.error('Error releasing payout:', err);
      toast.error('❌ Failed to release payout. Please try again.');
    } finally {
      setProcessingPayouts(prev => {
        const newSet = new Set(prev);
        newSet.delete(payoutId);
        return newSet;
      });
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatDate = (date: any) => {
    if (!date) return 'N/A';
    const dateObj = date.toDate ? date.toDate() : new Date(date);
    return dateObj.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <DollarSign className="h-6 w-6 text-green-600 dark:text-green-400 mr-2" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Pending Payouts ({pendingPayouts.length})
            </h3>
          </div>
          <button
            onClick={fetchPendingPayouts}
            disabled={loading}
            className="inline-flex items-center px-3 py-1 text-sm font-medium text-gray-700 dark:text-gray-200 bg-gray-100 dark:bg-gray-700 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 disabled:opacity-50"
          >
            <RefreshCw className={`h-4 w-4 mr-1 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        </div>
      </div>

      {error && (
        <div className="px-6 py-4 bg-red-50 dark:bg-red-900/20 border-b border-red-200 dark:border-red-800">
          <div className="flex items-center">
            <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400 mr-2" />
            <p className="text-red-800 dark:text-red-200">{error}</p>
          </div>
        </div>
      )}

      <div className="divide-y divide-gray-200 dark:divide-gray-700">
        {pendingPayouts.length === 0 ? (
          <div className="px-6 py-12 text-center">
            <CheckCircle className="mx-auto h-12 w-12 text-green-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No pending payouts</h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              All payouts have been processed or there are no completed orders awaiting payout.
            </p>
          </div>
        ) : (
          pendingPayouts.map((payout) => (
            <div key={payout.id} className="px-6 py-4 hover:bg-gray-50 dark:hover:bg-gray-700/50">
              <div className="flex items-center justify-between">
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0">
                      <div className="w-10 h-10 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center">
                        <User className="h-5 w-5 text-green-600 dark:text-green-400" />
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                          {payout.sellerName}
                        </p>
                        <span className="text-xs text-gray-500 dark:text-gray-400">→</span>
                        <p className="text-sm text-gray-600 dark:text-gray-300 truncate">
                          {payout.listingTitle}
                        </p>
                      </div>
                      <div className="flex items-center space-x-4 mt-1">
                        <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                          <Calendar className="h-3 w-3 mr-1" />
                          {formatDate(payout.createdAt)}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          Order: {payout.id.substring(0, 8)}...
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-4">
                  <div className="text-right">
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      {formatCurrency(payout.sellerAmount)}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      Total: {formatCurrency(payout.totalAmount)}
                    </div>
                  </div>
                  
                  <button
                    onClick={() => handleReleasePayout(payout.id)}
                    disabled={processingPayouts.has(payout.id)}
                    className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-green-600 hover:bg-green-700 disabled:bg-green-400 rounded-md transition-colors"
                  >
                    {processingPayouts.has(payout.id) ? (
                      <>
                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                        Processing...
                      </>
                    ) : (
                      <>
                        <CheckCircle className="h-4 w-4 mr-2" />
                        Release Payout
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default PayoutManagement;
