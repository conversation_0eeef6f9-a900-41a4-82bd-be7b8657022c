import React, { useState, useEffect } from 'react';
import {
  CreditCard,
  Search,
  Filter,
  Eye,
  RefreshCw,
  DollarSign,
  Calendar,
  User,
  Package,
  AlertTriangle,
  CheckCircle,
  Clock,
  XCircle,
  ArrowUpRight,
  ArrowDownLeft,
  Download,
  TrendingUp,
  BarChart3
} from 'lucide-react';
import { AdminStripeService, StripeMetrics } from '../../../services/adminStripeService';
import { AdminDataService } from '../../../services/AdminDataService';
import { logAdminAction } from '../../../utils/adminAuth';
import { useAuth } from '../../../hooks/useAuth';
import PayoutManagement from '../PayoutManagement';
import RefundManagement from '../RefundManagement';

interface Transaction {
  id: string;
  orderId: string;
  amount: number;
  currency: string;
  status: string;
  type: 'payment' | 'payout' | 'refund';
  buyerId?: string;
  sellerId?: string;
  buyerName?: string;
  sellerName?: string;
  listingTitle?: string;
  stripePaymentIntentId?: string;
  stripeTransferId?: string;
  createdAt: any;
  updatedAt?: any;
  metadata?: Record<string, any>;
}

const AdminTransactions: React.FC = () => {
  const { userProfile } = useAuth();
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [stripeMetrics, setStripeMetrics] = useState<StripeMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<string>('');
  const [selectedType, setSelectedType] = useState<string>('');
  const [showFilters, setShowFilters] = useState(false);

  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [platformMetrics, setPlatformMetrics] = useState<any>(null);

  // Modal states for replacing prompt()
  const [showReasonModal, setShowReasonModal] = useState(false);
  const [modalConfig, setModalConfig] = useState<{
    title: string;
    placeholder: string;
    action: string;
    transactionId: string;
    inputType?: 'text' | 'number';
    maxValue?: number;
  } | null>(null);
  const [reasonInput, setReasonInput] = useState('');

  const transactionStatuses = [
    'succeeded',
    'pending',
    'failed',
    'cancelled',
    'refunded',
    'disputed'
  ];

  const transactionTypes = [
    'payment',
    'payout',
    'refund'
  ];

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [metrics, recentTransactions] = await Promise.all([
        AdminStripeService.getStripeMetrics(),
        AdminStripeService.getRecentTransactions(50)
      ]);

      setStripeMetrics(metrics);

      // Convert orders to transaction format
      const formattedTransactions: Transaction[] = (recentTransactions || []).map((order: any) => ({
        id: order.id,
        orderId: order.id,
        amount: order.totalAmount || 0,
        currency: 'USD',
        status: order.status === 'payment_succeeded' ? 'succeeded' : order.status || 'pending',
        type: 'payment' as const,
        buyerId: order.buyerId || '',
        sellerId: order.sellerId || '',
        buyerName: order.buyerName || 'Unknown Buyer',
        sellerName: order.sellerName || 'Unknown Seller',
        listingTitle: order.listingTitle || 'Unknown Item',
        stripePaymentIntentId: order.stripePaymentIntentId,
        createdAt: order.createdAt || new Date(),
        updatedAt: order.updatedAt,
        metadata: order.metadata
      }));

      setTransactions(formattedTransactions);

      // Calculate comprehensive platform metrics
      const totalTransactionVolume = formattedTransactions.reduce((sum, t) => sum + t.amount, 0);
      const successfulTransactions = formattedTransactions.filter(t => t.status === 'succeeded');
      const platformFeeRate = 0.10; // 10% platform fee
      const textbookFeeRate = 0.08; // 8% for textbooks

      const platformRevenue = successfulTransactions.reduce((sum, t) => {
        const isTextbook = t.listingTitle?.toLowerCase().includes('textbook') ||
                          t.listingTitle?.toLowerCase().includes('book');
        const feeRate = isTextbook ? textbookFeeRate : platformFeeRate;
        return sum + (t.amount * feeRate);
      }, 0);

      const averageTransactionValue = successfulTransactions.length > 0
        ? totalTransactionVolume / successfulTransactions.length
        : 0;

      const transactionsByStatus = formattedTransactions.reduce((acc, t) => {
        acc[t.status] = (acc[t.status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const platformMetricsData = {
        totalTransactions: formattedTransactions.length,
        totalVolume: totalTransactionVolume,
        platformRevenue,
        averageTransactionValue,
        successfulTransactions: successfulTransactions.length,
        successRate: formattedTransactions.length > 0
          ? (successfulTransactions.length / formattedTransactions.length) * 100
          : 0,
        transactionsByStatus,
        monthlyGrowth: 0, // Will be calculated from real data when implemented
        revenueGrowth: 0 // Will be calculated from real data when implemented
      };

      setPlatformMetrics(platformMetricsData);
    } catch (err) {
      console.error('Error fetching transaction data:', err);
      setError('Failed to load transaction data. This may be because no orders exist yet.');
    } finally {
      setLoading(false);
    }
  };

  const handleTransactionAction = async (transactionId: string, action: string) => {
    try {
      if (userProfile) {
        await logAdminAction(userProfile, `transaction_${action}`, { transactionId });
      }

      switch (action) {
        case 'release_funds':
          setModalConfig({
            title: 'Release Funds to Seller',
            placeholder: 'Enter reason for releasing funds (optional)...',
            action: 'release_funds',
            transactionId,
            inputType: 'text'
          });
          setShowReasonModal(true);
          break;
        case 'refund':
          setModalConfig({
            title: 'Process Refund',
            placeholder: 'Enter reason for refund...',
            action: 'refund',
            transactionId,
            inputType: 'text'
          });
          setShowReasonModal(true);
          return; // Don't continue processing
        case 'block_payment':
          setModalConfig({
            title: 'Block Payment',
            placeholder: 'Enter reason for blocking payment...',
            action: 'block_payment',
            transactionId,
            inputType: 'text'
          });
          setShowReasonModal(true);
          return; // Don't continue processing
        case 'extend_escrow':
          setModalConfig({
            title: 'Extend Escrow',
            placeholder: 'Enter number of days to extend escrow (1-30)...',
            action: 'extend_escrow',
            transactionId,
            inputType: 'number',
            maxValue: 30
          });
          setShowReasonModal(true);
          return; // Don't continue processing
        case 'force_release':
          if (confirm('Are you sure you want to force release funds for this transaction?')) {
            try {
              await AdminStripeService.forceReleaseFunds(transactionId, 'Admin force release override');
              setError(null);
              // Refresh transactions
              fetchData();
            } catch (err) {
              setError('Failed to force release funds. Please try again.');
              console.error('Error force releasing funds:', err);
            }
          }
          break;
        case 'view_details':
          // Open transaction details in new tab or modal
          window.open(`/admin/transactions/${transactionId}`, '_blank');
          return; // Don't refresh for view action
      }

      // Refresh data
      await fetchData();
    } catch (err) {
      console.error(`Error performing ${action} on transaction:`, err);
    }
  };

  const handleModalSubmit = async () => {
    if (!modalConfig) return;

    try {
      const { action, transactionId } = modalConfig;

      switch (action) {
        case 'release_funds':
          if (confirm('⚠️ Are you sure you want to release funds to the seller? This action cannot be undone.')) {
            try {
              await AdminStripeService.forceReleaseFunds(transactionId, reasonInput.trim() || 'Admin manual release');
              setError(null);
              setSuccessMessage('✅ Funds successfully released to seller!');
              fetchData();
            } catch (err) {
              setError('❌ Failed to release funds. Please try again.');
              console.error('Error releasing funds:', err);
            }
          }
          break;
        case 'refund':
          if (confirm('⚠️ Are you sure you want to refund this transaction? This action cannot be undone.')) {
            if (!reasonInput.trim()) {
              setError('Please provide a reason for the refund.');
              return;
            }
            try {
              await AdminStripeService.refundTransaction(transactionId, undefined, reasonInput.trim());
              setError(null);
              setSuccessMessage('✅ Refund processed successfully!');
              fetchData();
            } catch (err) {
              setError('❌ Failed to process refund. Please try again.');
              console.error('Error processing refund:', err);
            }
          }
          break;
        case 'block_payment':
          if (confirm('Are you sure you want to block this payment?')) {
            await AdminDataService.updateOrder(transactionId, {
              status: 'payment_blocked',
              blockReason: reasonInput.trim(),
              blockedAt: new Date(),
              blockedBy: userProfile?.uid
            });
            setError(null);
            fetchData();
          }
          break;
        case 'extend_escrow': {
          const extensionDays = parseInt(reasonInput.trim());
          if (extensionDays > 0 && extensionDays <= 30) {
            if (confirm(`Extend escrow by ${extensionDays} days?`)) {
              const currentDate = new Date();
              const newReleaseDate = new Date(currentDate.getTime() + extensionDays * 24 * 60 * 60 * 1000);

              await AdminDataService.updateOrder(transactionId, {
                autoReleaseDate: newReleaseDate,
                escrowExtended: true,
                escrowExtensionDays: extensionDays,
                adminNotes: `Escrow extended by ${extensionDays} days`,
                extendedBy: userProfile?.uid,
                extendedAt: new Date()
              });
              setError(null);
              fetchData();
            }
          } else {
            setError('Please enter a valid number of days (1-30)');
            return;
          }
          break;
        }
      }

      // Close modal
      setShowReasonModal(false);
      setModalConfig(null);
      setReasonInput('');
    } catch (err) {
      console.error(`Error performing ${modalConfig.action} on transaction:`, err);
      setError(`Failed to ${modalConfig.action}. Please try again.`);
    }
  };

  const handleModalCancel = () => {
    setShowReasonModal(false);
    setModalConfig(null);
    setReasonInput('');
  };

  const filteredTransactions = transactions.filter(transaction => {
    const matchesSearch =
      transaction.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transaction.buyerName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transaction.sellerName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transaction.listingTitle?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = !selectedStatus || transaction.status === selectedStatus;
    const matchesType = !selectedType || transaction.type === selectedType;

    return matchesSearch && matchesStatus && matchesType;
  });

  const formatDate = (timestamp: any) => {
    if (!timestamp) return 'N/A';

    try {
      let date: Date;

      if (timestamp.toDate && typeof timestamp.toDate === 'function') {
        date = timestamp.toDate();
      } else if (timestamp.seconds && typeof timestamp.seconds === 'number') {
        date = new Date(timestamp.seconds * 1000);
      } else if (timestamp._seconds && typeof timestamp._seconds === 'number') {
        date = new Date(timestamp._seconds * 1000);
      } else {
        date = new Date(timestamp);
      }

      if (isNaN(date.getTime())) {
        return 'N/A';
      }

      return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
    } catch (error) {
      console.warn('Error formatting date:', error);
      return 'N/A';
    }
  };

  const formatCurrency = (amount: number, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency
    }).format(amount / 100); // Stripe amounts are in cents
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'succeeded': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300';
      case 'pending': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300';
      case 'failed': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300';
      case 'cancelled': return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300';
      case 'refunded': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300';
      case 'disputed': return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'succeeded': return <CheckCircle className="h-4 w-4" />;
      case 'pending': return <Clock className="h-4 w-4" />;
      case 'failed': return <XCircle className="h-4 w-4" />;
      case 'cancelled': return <XCircle className="h-4 w-4" />;
      case 'refunded': return <RefreshCw className="h-4 w-4" />;
      case 'disputed': return <AlertTriangle className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'payment': return <ArrowDownLeft className="h-4 w-4 text-green-600" />;
      case 'payout': return <ArrowUpRight className="h-4 w-4 text-blue-600" />;
      case 'refund': return <RefreshCw className="h-4 w-4 text-orange-600" />;
      default: return <CreditCard className="h-4 w-4" />;
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600 dark:text-gray-400">Loading transactions...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex">
            <AlertTriangle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                Error Loading Transactions
              </h3>
              <p className="mt-1 text-sm text-red-700 dark:text-red-300">{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Success/Error Messages */}
      {successMessage && (
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
          <div className="flex items-center">
            <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400 mr-2" />
            <p className="text-green-800 dark:text-green-200 font-medium">{successMessage}</p>
            <button
              onClick={() => setSuccessMessage(null)}
              className="ml-auto text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-200"
            >
              <XCircle className="h-4 w-4" />
            </button>
          </div>
        </div>
      )}

      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex items-center">
            <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400 mr-2" />
            <p className="text-red-800 dark:text-red-200 font-medium">{error}</p>
            <button
              onClick={() => setError(null)}
              className="ml-auto text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200"
            >
              <XCircle className="h-4 w-4" />
            </button>
          </div>
        </div>
      )}

      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">💳 Transactions Management</h2>
          <p className="text-gray-600 dark:text-gray-400">
            Monitor and manage payments, payouts, and refunds ({transactions.length} transactions)
          </p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={fetchData}
            className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </button>
          <button className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
            <Download className="h-4 w-4 mr-2" />
            Export
          </button>
        </div>
      </div>

      {/* Comprehensive Platform Metrics */}
      {platformMetrics && (
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          {/* Total Transaction Volume */}
          <div className="bg-white dark:bg-gray-800 overflow-hidden shadow-sm rounded-lg border border-gray-200 dark:border-gray-700">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <TrendingUp className="h-6 w-6 text-blue-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                      Total Transaction Volume
                    </dt>
                    <dd className="text-2xl font-semibold text-gray-900 dark:text-white">
                      {formatCurrency(platformMetrics.totalVolume)}
                    </dd>
                    <dd className="text-sm text-gray-600 dark:text-gray-400">
                      {platformMetrics.totalTransactions} transactions
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          {/* Platform Revenue */}
          <div className="bg-white dark:bg-gray-800 overflow-hidden shadow-sm rounded-lg border border-gray-200 dark:border-gray-700">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <DollarSign className="h-6 w-6 text-green-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                      Platform Revenue
                    </dt>
                    <dd className="text-2xl font-semibold text-gray-900 dark:text-white">
                      {formatCurrency(platformMetrics.platformRevenue)}
                    </dd>
                    <dd className="text-sm text-green-600 dark:text-green-400">
                      +{platformMetrics.revenueGrowth?.toFixed(1)}% this month
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          {/* Success Rate */}
          <div className="bg-white dark:bg-gray-800 overflow-hidden shadow-sm rounded-lg border border-gray-200 dark:border-gray-700">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CheckCircle className="h-6 w-6 text-green-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                      Success Rate
                    </dt>
                    <dd className="text-2xl font-semibold text-gray-900 dark:text-white">
                      {platformMetrics.successRate?.toFixed(1)}%
                    </dd>
                    <dd className="text-sm text-gray-600 dark:text-gray-400">
                      {platformMetrics.successfulTransactions} successful
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          {/* Average Transaction Value */}
          <div className="bg-white dark:bg-gray-800 overflow-hidden shadow-sm rounded-lg border border-gray-200 dark:border-gray-700">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <BarChart3 className="h-6 w-6 text-purple-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                      Average Transaction
                    </dt>
                    <dd className="text-2xl font-semibold text-gray-900 dark:text-white">
                      {formatCurrency(platformMetrics.averageTransactionValue)}
                    </dd>
                    <dd className="text-sm text-gray-600 dark:text-gray-400">
                      +{platformMetrics.monthlyGrowth?.toFixed(1)}% growth
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 overflow-hidden shadow-sm rounded-lg border border-gray-200 dark:border-gray-700">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Clock className="h-6 w-6 text-orange-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                      Pending Payouts
                    </dt>
                    <dd className="text-2xl font-semibold text-gray-900 dark:text-white">
                      {stripeMetrics?.pendingPayouts || 0}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Search and Filters */}
      <div className="bg-white dark:bg-gray-800 shadow-sm rounded-lg border border-gray-200 dark:border-gray-700 p-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search by transaction ID, user, or listing..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
            </div>
          </div>
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
          >
            <Filter className="h-4 w-4 mr-2" />
            Filters
          </button>
        </div>

        {/* Filter Options */}
        {showFilters && (
          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Status
                </label>
                <select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                >
                  <option value="">All Statuses</option>
                  {transactionStatuses.map(status => (
                    <option key={status} value={status}>
                      {status.charAt(0).toUpperCase() + status.slice(1)}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Type
                </label>
                <select
                  value={selectedType}
                  onChange={(e) => setSelectedType(e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                >
                  <option value="">All Types</option>
                  {transactionTypes.map(type => (
                    <option key={type} value={type}>
                      {type.charAt(0).toUpperCase() + type.slice(1)}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Transactions Table */}
      <div className="bg-white dark:bg-gray-800 shadow-sm rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
        {filteredTransactions.length === 0 ? (
          <div className="px-4 py-12">
            <div className="text-center">
              <CreditCard className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No transactions found</h3>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                {searchTerm ? 'Try adjusting your search terms.' : 'No transactions match the current filters.'}
              </p>
            </div>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Transaction
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Amount
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Parties
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Listing
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {filteredTransactions.map((transaction) => (
                  <tr key={transaction.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0">
                          {getTypeIcon(transaction.type)}
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {transaction.id.substring(0, 8)}...
                          </div>
                          <div className="flex items-center">
                            <span className={`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(transaction.status)}`}>
                              {getStatusIcon(transaction.status)}
                              <span className="ml-1">{transaction.status}</span>
                            </span>
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {formatCurrency(transaction.amount)}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {transaction.currency.toUpperCase()}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white">
                        <div className="flex items-center mb-1">
                          <User className="h-3 w-3 mr-1 text-green-500" />
                          <span className="text-xs text-gray-500">Buyer:</span>
                          <span className="ml-1">{transaction.buyerName || 'Unknown'}</span>
                        </div>
                        <div className="flex items-center">
                          <User className="h-3 w-3 mr-1 text-blue-500" />
                          <span className="text-xs text-gray-500">Seller:</span>
                          <span className="ml-1">{transaction.sellerName || 'Unknown'}</span>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center text-sm text-gray-900 dark:text-white">
                        <Package className="h-4 w-4 mr-2 text-gray-400" />
                        <span className="truncate max-w-xs">
                          {transaction.listingTitle || 'N/A'}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                        <Calendar className="h-4 w-4 mr-2" />
                        {formatDate(transaction.createdAt)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end space-x-1">
                        {/* View Details */}
                        <button
                          onClick={() => handleTransactionAction(transaction.id, 'view_details')}
                          className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 p-1"
                          title="View Details"
                        >
                          <Eye className="h-4 w-4" />
                        </button>

                        {/* Release Funds - Enhanced Button */}
                        {(transaction.status === 'pending' || transaction.status === 'succeeded') && (
                          <button
                            onClick={() => handleTransactionAction(transaction.id, 'release_funds')}
                            className="inline-flex items-center px-3 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800 hover:bg-green-200 dark:bg-green-900/20 dark:text-green-400 dark:hover:bg-green-900/30 transition-colors"
                            title="Release Funds to Seller"
                          >
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Release
                          </button>
                        )}

                        {/* Refund - Enhanced Button */}
                        {transaction.status === 'succeeded' && (
                          <button
                            onClick={() => handleTransactionAction(transaction.id, 'refund')}
                            className="inline-flex items-center px-3 py-1 text-xs font-medium rounded-full bg-orange-100 text-orange-800 hover:bg-orange-200 dark:bg-orange-900/20 dark:text-orange-400 dark:hover:bg-orange-900/30 transition-colors"
                            title="Process Full Refund"
                          >
                            <RefreshCw className="h-3 w-3 mr-1" />
                            Refund
                          </button>
                        )}

                        {/* Block Payment */}
                        {(transaction.status === 'pending' || transaction.status === 'succeeded') && (
                          <button
                            onClick={() => handleTransactionAction(transaction.id, 'block_payment')}
                            className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 p-1"
                            title="Block Payment"
                          >
                            <XCircle className="h-4 w-4" />
                          </button>
                        )}

                        {/* Extend Escrow */}
                        {transaction.status === 'pending' && (
                          <button
                            onClick={() => handleTransactionAction(transaction.id, 'extend_escrow')}
                            className="text-yellow-600 hover:text-yellow-900 dark:text-yellow-400 dark:hover:text-yellow-300 p-1"
                            title="Extend Escrow"
                          >
                            <Clock className="h-4 w-4" />
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Enhanced Payout & Refund Management */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <PayoutManagement />
        <RefundManagement />
      </div>

      {/* Reason Input Modal */}
      {showReasonModal && modalConfig && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              {modalConfig.title}
            </h3>

            <div className="mb-4">
              <label htmlFor="transaction-reason-input" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {modalConfig.action === 'extend_escrow' ? 'Number of Days' : 'Reason'}
              </label>
              <input
                id="transaction-reason-input"
                name="reason"
                type={modalConfig.inputType || 'text'}
                value={reasonInput}
                onChange={(e) => setReasonInput(e.target.value)}
                placeholder={modalConfig.placeholder}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                required
                {...(modalConfig.inputType === 'number' && {
                  min: 1,
                  max: modalConfig.maxValue || 100
                })}
              />
            </div>

            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={handleModalCancel}
                className="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleModalSubmit}
                disabled={!reasonInput.trim()}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                Submit
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminTransactions;
