import React, { useState } from 'react';
import {
  TrendingUp,
  Users,
  DollarSign,
  Package,
  ShoppingCart,
  RefreshCw,
  Download,
  Calendar,
  Target,
  Activity,
  AlertTriangle,
  CheckCircle,
  BarChart3,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>
} from 'lucide-react';
import {
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>hart as <PERSON><PERSON><PERSON>Bar<PERSON><PERSON>,
  Bar,
  <PERSON><PERSON>hart as RechartsPie<PERSON>hart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { useYCMetrics } from '../../../hooks/useYCMetrics';
import { collection, addDoc } from 'firebase/firestore';
import { firestore, functions } from '../../../firebase/config';
import { httpsCallable } from 'firebase/functions';
import { useAuth } from '../../../hooks/useAuth';
import toast from 'react-hot-toast';

const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4'];

const YCMetrics: React.FC = () => {
  const { userProfile } = useAuth();
  const { 
    metricsData, 
    isLoading, 
    isRefreshing, 
    error, 
    refreshMetrics, 
    exportToCSV, 
    exportSummaryToCSV 
  } = useYCMetrics(30);

  const [selectedTimeRange, setSelectedTimeRange] = useState(30);
  const [showInsightForm, setShowInsightForm] = useState(false);
  const [insightText, setInsightText] = useState('');
  const [insightSource, setInsightSource] = useState('');
  const [insightImpact, setInsightImpact] = useState<'low' | 'medium' | 'high'>('medium');
  const [isTriggeringMetrics, setIsTriggeringMetrics] = useState(false);

  const handleAddInsight = async () => {
    if (!insightText.trim() || !userProfile) return;

    try {
      await addDoc(collection(firestore, 'founder_insights'), {
        timestamp: new Date(),
        insight: insightText.trim(),
        source: insightSource.trim() || 'General observation',
        impactEstimate: insightImpact,
        addedBy: userProfile.uid,
        addedByName: userProfile.displayName || userProfile.email
      });

      toast.success('Insight added successfully!');
      setInsightText('');
      setInsightSource('');
      setInsightImpact('medium');
      setShowInsightForm(false);
    } catch (err) {
      console.error('Error adding insight:', err);
      toast.error('Failed to add insight');
    }
  };

  const handleTriggerMetrics = async () => {
    try {
      setIsTriggeringMetrics(true);

      const triggerDailyMetrics = httpsCallable(functions, 'triggerDailyMetrics');
      const result = await triggerDailyMetrics();
      const data = result.data as { success: boolean; message: string };

      if (data.success) {
        toast.success('Daily metrics logged successfully!');
        // Refresh the metrics data
        await refreshMetrics();
      } else {
        throw new Error(data.message || 'Failed to trigger metrics');
      }
    } catch (err) {
      console.error('Error triggering metrics:', err);
      toast.error('Failed to trigger daily metrics');
    } finally {
      setIsTriggeringMetrics(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatDate = (dateStr: string) => {
    return new Date(dateStr).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <RefreshCw className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
            <p className="text-gray-600 dark:text-gray-400">Loading YC metrics...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
          <div className="flex items-center">
            <AlertTriangle className="h-6 w-6 text-red-600 dark:text-red-400 mr-3" />
            <div>
              <h3 className="text-lg font-medium text-red-800 dark:text-red-200">Failed to Load Metrics</h3>
              <p className="text-red-600 dark:text-red-400 mt-1">{error}</p>
            </div>
          </div>
          <button
            onClick={refreshMetrics}
            className="mt-4 inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </button>
        </div>
      </div>
    );
  }

  if (!metricsData) return null;

  const { summary, dailyMetrics } = metricsData;

  // Prepare chart data
  const chartData = dailyMetrics.slice().reverse().map(metric => ({
    date: formatDate(metric.date),
    fullDate: metric.date,
    dau: metric.dailyActiveUsers,
    signups: metric.newSignups,
    gmv: metric.gmvToday,
    revenue: metric.platformRevenueToday,
    orders: metric.ordersPlaced,
    completed: metric.ordersCompleted,
    disputed: metric.ordersDisputed,
    listings: metric.newListings
  }));

  const retentionData = [
    { name: 'Day 1', value: summary.retention.avgDay1Retention, color: COLORS[0] },
    { name: 'Day 7', value: summary.retention.avgDay7Retention, color: COLORS[1] },
    { name: 'Churned', value: 100 - summary.retention.avgDay1Retention, color: COLORS[3] }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
            <Target className="h-7 w-7 mr-2 text-blue-600" />
            YC Metrics Dashboard
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            30-day performance metrics for Y Combinator application
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={handleTriggerMetrics}
            disabled={isTriggeringMetrics}
            className="inline-flex items-center px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 disabled:opacity-50"
          >
            <Activity className={`h-4 w-4 mr-2 ${isTriggeringMetrics ? 'animate-spin' : ''}`} />
            {isTriggeringMetrics ? 'Logging...' : 'Log Today\'s Metrics'}
          </button>
          <button
            onClick={() => setShowInsightForm(true)}
            className="inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700"
          >
            <Target className="h-4 w-4 mr-2" />
            Add Insight
          </button>
          <button
            onClick={exportSummaryToCSV}
            className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
          >
            <Download className="h-4 w-4 mr-2" />
            Export Summary
          </button>
          <button
            onClick={exportToCSV}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            <Download className="h-4 w-4 mr-2" />
            Export Data
          </button>
          <button
            onClick={refreshMetrics}
            disabled={isRefreshing}
            className="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 disabled:opacity-50"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            {isRefreshing ? 'Refreshing...' : 'Refresh'}
          </button>
        </div>
      </div>

      {/* Key Metrics Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center">
            <Users className="h-8 w-8 text-blue-600 dark:text-blue-400" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Signups</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {summary.userGrowth.totalNewSignups.toLocaleString()}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {summary.userGrowth.avgDailySignups.toFixed(1)}/day avg
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center">
            <DollarSign className="h-8 w-8 text-green-600 dark:text-green-400" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total GMV</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {formatCurrency(summary.revenue.totalGMV)}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {formatCurrency(summary.revenue.avgDailyGMV)}/day avg
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center">
            <ShoppingCart className="h-8 w-8 text-purple-600 dark:text-purple-400" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Orders Completed</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {summary.orders.totalOrdersCompleted.toLocaleString()}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {summary.orders.completionRate.toFixed(1)}% completion rate
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center">
            <Activity className="h-8 w-8 text-orange-600 dark:text-orange-400" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Peak DAU</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {summary.userGrowth.peakDAU.toLocaleString()}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {summary.userGrowth.avgDAU.toFixed(0)} avg DAU
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* User Growth Chart */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
            <LineChart className="h-5 w-5 mr-2" />
            Daily Active Users & Signups
          </h3>
          <ResponsiveContainer width="100%" height={300}>
            <RechartsLineChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis dataKey="date" className="text-gray-600 dark:text-gray-400" fontSize={12} />
              <YAxis className="text-gray-600 dark:text-gray-400" fontSize={12} />
              <Tooltip 
                contentStyle={{
                  backgroundColor: 'var(--tooltip-bg)',
                  border: '1px solid var(--tooltip-border)',
                  borderRadius: '8px'
                }}
              />
              <Legend />
              <Line type="monotone" dataKey="dau" stroke="#3B82F6" strokeWidth={2} name="Daily Active Users" />
              <Line type="monotone" dataKey="signups" stroke="#10B981" strokeWidth={2} name="New Signups" />
            </RechartsLineChart>
          </ResponsiveContainer>
        </div>

        {/* Revenue Chart */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
            <DollarSign className="h-5 w-5 mr-2" />
            Daily GMV & Revenue
          </h3>
          <ResponsiveContainer width="100%" height={300}>
            <RechartsLineChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis dataKey="date" className="text-gray-600 dark:text-gray-400" fontSize={12} />
              <YAxis className="text-gray-600 dark:text-gray-400" fontSize={12} />
              <Tooltip 
                contentStyle={{
                  backgroundColor: 'var(--tooltip-bg)',
                  border: '1px solid var(--tooltip-border)',
                  borderRadius: '8px'
                }}
                formatter={(value: any) => [`$${value.toFixed(2)}`, '']}
              />
              <Legend />
              <Line type="monotone" dataKey="gmv" stroke="#10B981" strokeWidth={2} name="Daily GMV" />
              <Line type="monotone" dataKey="revenue" stroke="#F59E0B" strokeWidth={2} name="Platform Revenue" />
            </RechartsLineChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Orders & Retention Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Orders Chart */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
            <BarChart3 className="h-5 w-5 mr-2" />
            Daily Orders
          </h3>
          <ResponsiveContainer width="100%" height={300}>
            <RechartsBarChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis dataKey="date" className="text-gray-600 dark:text-gray-400" fontSize={12} />
              <YAxis className="text-gray-600 dark:text-gray-400" fontSize={12} />
              <Tooltip
                contentStyle={{
                  backgroundColor: 'var(--tooltip-bg)',
                  border: '1px solid var(--tooltip-border)',
                  borderRadius: '8px'
                }}
              />
              <Legend />
              <Bar dataKey="orders" fill="#3B82F6" name="Orders Placed" />
              <Bar dataKey="completed" fill="#10B981" name="Completed" />
              <Bar dataKey="disputed" fill="#EF4444" name="Disputed" />
            </RechartsBarChart>
          </ResponsiveContainer>
        </div>

        {/* Retention Pie Chart */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
            <PieChart className="h-5 w-5 mr-2" />
            User Retention
          </h3>
          <ResponsiveContainer width="100%" height={300}>
            <RechartsPieChart>
              <Pie
                data={retentionData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, value }) => `${name}: ${value.toFixed(1)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {retentionData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip
                contentStyle={{
                  backgroundColor: 'var(--tooltip-bg)',
                  border: '1px solid var(--tooltip-border)',
                  borderRadius: '8px'
                }}
                formatter={(value: any) => [`${value.toFixed(1)}%`, '']}
              />
            </RechartsPieChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Detailed Metrics Table */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Daily Metrics Breakdown
          </h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  DAU
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Signups
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Orders
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  GMV
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Revenue
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Listings
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {dailyMetrics.slice(0, 10).map((metric) => (
                <tr key={metric.date} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    {formatDate(metric.date)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    {metric.dailyActiveUsers.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    {metric.newSignups.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    {metric.ordersPlaced.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    {formatCurrency(metric.gmvToday)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    {formatCurrency(metric.platformRevenueToday)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    {metric.newListings.toLocaleString()}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Founder Insights Form Modal */}
      {showInsightForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Add Founder Insight
            </h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Insight *
                </label>
                <textarea
                  value={insightText}
                  onChange={(e) => setInsightText(e.target.value)}
                  placeholder="What did you learn or observe?"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  rows={3}
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Source
                </label>
                <input
                  type="text"
                  value={insightSource}
                  onChange={(e) => setInsightSource(e.target.value)}
                  placeholder="e.g., user feedback, feature test, analytics"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Impact Estimate
                </label>
                <select
                  value={insightImpact}
                  onChange={(e) => setInsightImpact(e.target.value as 'low' | 'medium' | 'high')}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                >
                  <option value="low">Low Impact</option>
                  <option value="medium">Medium Impact</option>
                  <option value="high">High Impact</option>
                </select>
              </div>
            </div>
            <div className="flex space-x-3 mt-6">
              <button
                onClick={() => setShowInsightForm(false)}
                className="flex-1 px-4 py-2 text-gray-700 dark:text-gray-200 bg-gray-100 dark:bg-gray-700 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600"
              >
                Cancel
              </button>
              <button
                onClick={handleAddInsight}
                disabled={!insightText.trim()}
                className="flex-1 px-4 py-2 text-white bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 rounded-md"
              >
                Add Insight
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default YCMetrics;
