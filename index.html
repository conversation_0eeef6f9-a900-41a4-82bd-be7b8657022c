<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/icons/icon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover, interactive-widget=resizes-content" />
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' 'wasm-unsafe-eval' https://www.gstatic.com https://www.googleapis.com https://apis.google.com https://www.googletagmanager.com https://js.stripe.com https://api.reeflex.ai https://browser.sentry-cdn.com https://*.sentry-cdn.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: blob: https:; media-src 'self' blob:; connect-src 'self' https://*.googleapis.com https://*.firebase.googleapis.com https://*.firebaseio.com https://*.cloudfunctions.net https://firestore.googleapis.com https://api.stripe.com https://connect.stripe.com https://api.reeflex.ai https://sentry.io https://*.sentry.io https://www.google-analytics.com https://www.googletagmanager.com wss://*.firebaseio.com; frame-src 'self' https://js.stripe.com https://checkout.stripe.com https://connect.stripe.com https://*.stripe.com https://*.firebaseapp.com https://*.google.com https://accounts.google.com; object-src 'none'; base-uri 'self'; form-action 'self' https://checkout.stripe.com https://connect.stripe.com; upgrade-insecure-requests; block-all-mixed-content">
    <meta name="theme-color" content="#f9a826" />
    <meta name="description" content="A student-exclusive marketplace app for college students" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="apple-mobile-web-app-title" content="Hive Campus" />
    
    <!-- PWA manifest -->
    <link rel="manifest" href="/manifest.json" />
    
    <!-- iOS icons -->
    <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
    <link rel="apple-touch-icon" sizes="152x152" href="/apple-touch-icon.png" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="apple-touch-icon" sizes="167x167" href="/apple-touch-icon.png" />
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Stripe preconnect for better performance -->
    <link rel="preconnect" href="https://js.stripe.com">
    <link rel="preconnect" href="https://api.stripe.com">
    
    <title>Hive Campus - Student Marketplace</title>
  </head>
  <body>
    <div id="root">
      <!-- Fallback content while React loads -->
      <div id="loading-fallback" style="
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        font-family: 'Inter', sans-serif;
        text-align: center;
        padding: 20px;
      ">
        <div style="
          background: rgba(255,255,255,0.1);
          backdrop-filter: blur(10px);
          border-radius: 20px;
          padding: 40px;
          max-width: 500px;
          width: 100%;
        ">
          <h1 style="font-size: 2.5rem; margin-bottom: 1rem; font-weight: 700;">
            🐝 Hive Campus
          </h1>
          <p style="font-size: 1.2rem; margin-bottom: 2rem; opacity: 0.9;">
            Student Marketplace Loading...
          </p>
          <div style="
            width: 50px;
            height: 50px;
            border: 3px solid rgba(255,255,255,0.3);
            border-top: 3px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto;
          "></div>
          <p style="font-size: 0.9rem; margin-top: 2rem; opacity: 0.7;">
            If this takes too long, try refreshing the page
          </p>
        </div>
      </div>
    </div>
    <style>
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
    <script type="module" src="/src/main.tsx"></script>
    <script>
      // Hide fallback once React loads
      setTimeout(() => {
        const fallback = document.getElementById('loading-fallback');
        const root = document.getElementById('root');
        if (fallback && root && root.children.length > 1) {
          fallback.style.display = 'none';
        }
      }, 2000);
    </script>
  </body>
</html>