import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { Search as SearchIcon, Filter, ChevronDown, Grid, List, Grid3X3, Eye, X, ShoppingBag, MapPin, Clock, TrendingUp, Sparkles } from 'lucide-react';
import { useLocation, Link } from 'react-router-dom';
import { useListings } from '../hooks/useListings';
import { searchCategories, getCategoryById } from '../constants/categories';

const Search: React.FC = () => {
  const location = useLocation();
  const searchInputRef = useRef<HTMLInputElement>(null);
  const { fetchListings, listings, isLoading: listingsLoading } = useListings();

  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedSort, setSelectedSort] = useState('relevance');
  const [selectedCondition, setSelectedCondition] = useState('all');
  const [selectedPriceRange, setSelectedPriceRange] = useState('all');
  const [selectedListingType, setSelectedListingType] = useState('all');
  const [layoutMode, setLayoutMode] = useState<'grid' | 'list' | 'compact'>('grid');
  const [showCategoryDropdown, setShowCategoryDropdown] = useState(false);
  const [showSortDropdown, setShowSortDropdown] = useState(false);
  const [showFilterDropdown, setShowFilterDropdown] = useState(false);
  const [showListingTypeDropdown, setShowListingTypeDropdown] = useState(false);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedSuggestionIndex, setSelectedSuggestionIndex] = useState(-1);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const [trendingSearches, setTrendingSearches] = useState<string[]>([]);
  const [hasSearched, setHasSearched] = useState(false);

  // Get current listings
  const currentListings = listings || [];

  // Categories
  const categories = searchCategories;

  const listingTypes = [
    { id: 'all', name: 'All Types', icon: '🏪' },
    { id: 'sell', name: 'For Sale', icon: '💰' },
    { id: 'rent', name: 'For Rent', icon: '📅' },
    { id: 'auction', name: 'Auction', icon: '🔨' }
  ];

  const conditions = [
    { id: 'all', name: 'All Conditions' },
    { id: 'new', name: 'New' },
    { id: 'like-new', name: 'Like New' },
    { id: 'good', name: 'Good' },
    { id: 'fair', name: 'Fair' },
    { id: 'poor', name: 'Poor' }
  ];

  const priceRanges = [
    { id: 'all', name: 'All Prices' },
    { id: '0-25', name: 'Under $25' },
    { id: '25-50', name: '$25 - $50' },
    { id: '50-100', name: '$50 - $100' },
    { id: '100-250', name: '$100 - $250' },
    { id: '250-500', name: '$250 - $500' },
    { id: '500+', name: '$500+' }
  ];

  const sortOptions = [
    { id: 'relevance', name: 'Most Relevant' },
    { id: 'newest', name: 'Newest First' },
    { id: 'oldest', name: 'Oldest First' },
    { id: 'price-low', name: 'Price: Low to High' },
    { id: 'price-high', name: 'Price: High to Low' },
    { id: 'popular', name: 'Most Popular' }
  ];

  // Load initial data and recent searches
  useEffect(() => {
    fetchListings();
    
    // Load recent searches from localStorage
    const saved = localStorage.getItem('recentSearches');
    if (saved) {
      setRecentSearches(JSON.parse(saved));
    }

    // Set trending searches (could be fetched from API in real app)
    setTrendingSearches([
      'iPhone 15',
      'MacBook Pro',
      'Textbooks',
      'Gaming Chair',
      'AirPods',
      'Dorm Furniture'
    ]);

    // Focus search input on mount
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [fetchListings]);

  // Parse URL parameters on mount
  useEffect(() => {
    const urlParams = new URLSearchParams(location.search);
    const query = urlParams.get('q');
    const category = urlParams.get('category');
    
    if (query) {
      setSearchQuery(query);
      setHasSearched(true);
    }
    if (category && category !== 'all') {
      setSelectedCategory(category);
    }
  }, [location.search]);

  // Memoized filtered and sorted listings
  const filteredListings = useMemo(() => {
    let filtered = [...currentListings];

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(listing =>
        listing.title.toLowerCase().includes(query) ||
        listing.description.toLowerCase().includes(query) ||
        listing.category.toLowerCase().includes(query)
      );
    }

    // Apply category filter
    if (selectedCategory !== 'all') {
      const selectedCategoryData = getCategoryById(selectedCategory);
      if (selectedCategoryData) {
        // Filter by backend category value
        filtered = filtered.filter(listing => listing.category === selectedCategoryData.backendValue);
      }
    }

    // Apply condition filter
    if (selectedCondition !== 'all') {
      filtered = filtered.filter(listing => listing.condition === selectedCondition);
    }

    // Apply listing type filter
    if (selectedListingType !== 'all') {
      filtered = filtered.filter(listing => listing.type === selectedListingType);
    }

    // Apply price range filter
    if (selectedPriceRange !== 'all') {
      if (selectedPriceRange === '500+') {
        filtered = filtered.filter(listing => listing.price >= 500);
      } else {
        const minPrice = parseInt(selectedPriceRange.split('-')[0]);
        const maxPrice = parseInt(selectedPriceRange.split('-')[1]);
        filtered = filtered.filter(listing => 
          listing.price >= minPrice && listing.price <= maxPrice
        );
      }
    }

    return filtered;
  }, [currentListings, searchQuery, selectedCategory, selectedCondition, selectedListingType, selectedPriceRange]);

  // Memoized sorted listings
  const sortedListings = useMemo(() => {
    const sorted = [...filteredListings];

    switch (selectedSort) {
      case 'newest':
        return sorted.sort((a, b) => {
          const aTime = (a.createdAt as any)?.toDate?.() || new Date((a.createdAt as any));
          const bTime = (b.createdAt as any)?.toDate?.() || new Date((b.createdAt as any));
          return bTime.getTime() - aTime.getTime();
        });
      case 'oldest':
        return sorted.sort((a, b) => {
          const aTime = (a.createdAt as any)?.toDate?.() || new Date((a.createdAt as any));
          const bTime = (b.createdAt as any)?.toDate?.() || new Date((b.createdAt as any));
          return aTime.getTime() - bTime.getTime();
        });
      case 'price-low':
        return sorted.sort((a, b) => a.price - b.price);
      case 'price-high':
        return sorted.sort((a, b) => b.price - a.price);
      case 'popular':
        return sorted.sort((a, b) => ((b as any).views || 0) - ((a as any).views || 0));
      case 'relevance':
      default:
        return sorted;
    }
  }, [filteredListings, selectedSort]);

  // Generate search suggestions
  const generateSuggestions = useCallback((query: string) => {
    if (!query.trim()) {
      setSuggestions([]);
      setShowSuggestions(false);
      return;
    }

    const queryLower = query.toLowerCase();
    const suggestionMap = new Map<string, number>();

    currentListings.forEach(listing => {
      // Title matches
      if (listing.title.toLowerCase().includes(queryLower)) {
        suggestionMap.set(listing.title, 100);
      }

      // Category matches
      if (listing.category.toLowerCase().includes(queryLower)) {
        const categoryData = categories.find(c => c.backendValue === listing.category);
        const categoryName = categoryData?.name || listing.category;
        suggestionMap.set(categoryName, 80);
      }

      // Description matches (lower priority)
      if (listing.description.toLowerCase().includes(queryLower)) {
        const words = listing.description.split(' ').filter(word =>
          word.toLowerCase().includes(queryLower) && word.length > 2
        );
        words.slice(0, 2).forEach(word => {
          suggestionMap.set(word, 60);
        });
      }

      // Universities that match
      if (listing.university?.toLowerCase().includes(queryLower)) {
        suggestionMap.set(`in ${listing.university}`, 50);
      }
    });

    // Sort by priority and limit to 6 suggestions
    const suggestionsArray = Array.from(suggestionMap.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 6)
      .map(([suggestion]) => suggestion);

    setSuggestions(suggestionsArray);
    setShowSuggestions(suggestionsArray.length > 0);
  }, [currentListings, categories]);

  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchQuery(value);
    setSelectedSuggestionIndex(-1);
    generateSuggestions(value);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!showSuggestions) return;

    if (e.key === 'ArrowDown') {
      e.preventDefault();
      setSelectedSuggestionIndex(prev =>
        prev < suggestions.length - 1 ? prev + 1 : prev
      );
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      setSelectedSuggestionIndex(prev => prev > 0 ? prev - 1 : -1);
    } else if (e.key === 'Enter') {
      e.preventDefault();
      if (selectedSuggestionIndex >= 0) {
        setSearchQuery(suggestions[selectedSuggestionIndex]);
        setShowSuggestions(false);
      }
      handleSearchSubmit(e);
    } else if (e.key === 'Escape') {
      setShowSuggestions(false);
      setSelectedSuggestionIndex(-1);
    }
  };

  const clearSearch = () => {
    setSearchQuery('');
    setSuggestions([]);
    setShowSuggestions(false);
    setSelectedSuggestionIndex(-1);
    setHasSearched(false);
  };

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setShowSuggestions(false);
    if (searchQuery.trim()) {
      setHasSearched(true);

      // Save to recent searches
      const newRecentSearches = [searchQuery, ...recentSearches.filter(s => s !== searchQuery)].slice(0, 5);
      setRecentSearches(newRecentSearches);
      localStorage.setItem('recentSearches', JSON.stringify(newRecentSearches));
    }
  };

  const handleSuggestionClick = (suggestion: string) => {
    setSearchQuery(suggestion);
    setShowSuggestions(false);
    setHasSearched(true);

    // Save to recent searches
    const newRecentSearches = [suggestion, ...recentSearches.filter(s => s !== suggestion)].slice(0, 5);
    setRecentSearches(newRecentSearches);
    localStorage.setItem('recentSearches', JSON.stringify(newRecentSearches));
  };

  const handleRecentSearchClick = (search: string) => {
    setSearchQuery(search);
    setHasSearched(true);
  };

  const handleTrendingSearchClick = (search: string) => {
    setSearchQuery(search);
    setHasSearched(true);
  };

  const clearRecentSearches = () => {
    setRecentSearches([]);
    localStorage.removeItem('recentSearches');
  };

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      setShowCategoryDropdown(false);
      setShowSortDropdown(false);
      setShowFilterDropdown(false);
      setShowListingTypeDropdown(false);
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, []);

  // Close dropdowns on scroll
  useEffect(() => {
    const handleScroll = () => {
      setShowCategoryDropdown(false);
      setShowSortDropdown(false);
      setShowFilterDropdown(false);
      setShowListingTypeDropdown(false);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Search Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center space-x-4">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Search</h1>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-6">
        {/* Search Bar Section */}
        <div className="mb-8">
          <form onSubmit={handleSearchSubmit} className="max-w-2xl mx-auto relative">
            <SearchIcon className="absolute left-4 top-4 w-6 h-6 text-gray-400" />
            <input
              ref={searchInputRef}
              type="text"
              value={searchQuery}
              onChange={handleSearchInputChange}
              onKeyDown={handleKeyDown}
              onFocus={() => searchQuery && setShowSuggestions(true)}
              onBlur={() => setTimeout(() => setShowSuggestions(false), 150)}
              placeholder="Search for textbooks, electronics, furniture..."
              className="w-full pl-12 pr-20 py-4 text-lg rounded-2xl border border-gray-300 dark:border-gray-600 focus:ring-4 focus:ring-primary-500/25 focus:border-primary-500 text-gray-900 dark:text-white dark:bg-gray-800"
            />
            {searchQuery && (
              <button
                type="button"
                onClick={clearSearch}
                className="absolute right-4 top-4 p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <X className="w-5 h-5" />
              </button>
            )}

            {/* Search Suggestions */}
            {showSuggestions && suggestions.length > 0 && (
              <div className="absolute top-full left-0 right-0 mt-2 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-lg z-50 max-h-64 overflow-y-auto">
                {suggestions.map((suggestion, index) => (
                  <button
                    key={suggestion}
                    type="button"
                    onClick={() => handleSuggestionClick(suggestion)}
                    className={`w-full text-left px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
                      index === selectedSuggestionIndex ? 'bg-gray-50 dark:bg-gray-700' : ''
                    } ${index === 0 ? 'rounded-t-xl' : ''} ${index === suggestions.length - 1 ? 'rounded-b-xl' : ''}`}
                  >
                    <div className="flex items-center space-x-3">
                      <SearchIcon className="w-4 h-4 text-gray-400" />
                      <span className="text-gray-900 dark:text-white">{suggestion}</span>
                    </div>
                  </button>
                ))}
              </div>
            )}
          </form>
        </div>

        {/* Search State Content */}
        {!hasSearched ? (
          /* Initial Search State */
          <div className="space-y-8">
            {/* Quick Category Filters */}
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                <Sparkles className="w-5 h-5 mr-2 text-primary-500" />
                Browse Categories
              </h2>
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3">
                {categories.slice(1, 13).map((category) => (
                  <button
                    key={category.id}
                    onClick={() => {
                      setSelectedCategory(category.id);
                      setHasSearched(true);
                    }}
                    className="p-4 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 hover:border-primary-500 dark:hover:border-primary-400 transition-all duration-200 text-center group"
                  >
                    <div className="text-sm font-medium text-gray-900 dark:text-white group-hover:text-primary-600 dark:group-hover:text-primary-400">
                      {category.name}
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* Recent Searches */}
            {recentSearches.length > 0 && (
              <div>
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-white flex items-center">
                    <Clock className="w-5 h-5 mr-2 text-gray-500" />
                    Recent Searches
                  </h2>
                  <button
                    onClick={clearRecentSearches}
                    className="text-sm text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                  >
                    Clear All
                  </button>
                </div>
                <div className="flex flex-wrap gap-2">
                  {recentSearches.map((search, index) => (
                    <button
                      key={index}
                      onClick={() => handleRecentSearchClick(search)}
                      className="px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors text-sm"
                    >
                      {search}
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Trending Searches */}
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                <TrendingUp className="w-5 h-5 mr-2 text-orange-500" />
                Trending Searches
              </h2>
              <div className="flex flex-wrap gap-2">
                {trendingSearches.map((search, index) => (
                  <button
                    key={index}
                    onClick={() => handleTrendingSearchClick(search)}
                    className="px-4 py-2 bg-gradient-to-r from-orange-100 to-red-100 dark:from-orange-900/30 dark:to-red-900/30 text-orange-700 dark:text-orange-300 rounded-full hover:from-orange-200 hover:to-red-200 dark:hover:from-orange-900/50 dark:hover:to-red-900/50 transition-all duration-200 text-sm font-medium"
                  >
                    🔥 {search}
                  </button>
                ))}
              </div>
            </div>
          </div>
        ) : (
          /* Search Results State */
          <div className="space-y-6">
            {/* Search Results Header */}
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                  {searchQuery ? `Search Results for "${searchQuery}"` : 'Browse Results'}
                </h2>
                <p className="text-gray-600 dark:text-gray-400 mt-1">
                  {sortedListings.length} {sortedListings.length === 1 ? 'item' : 'items'} found
                </p>
              </div>
              <button
                onClick={clearSearch}
                className="flex items-center space-x-2 px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
              >
                <X className="w-4 h-4" />
                <span>Clear Search</span>
              </button>
            </div>

            {/* Filters Bar */}
            <div className="flex flex-wrap items-center gap-3 p-4 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700">
              {/* Category Filter */}
              <div className="relative">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    setShowCategoryDropdown(!showCategoryDropdown);
                  }}
                  className="flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                >
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {categories.find(c => c.id === selectedCategory)?.name || 'Category'}
                  </span>
                  <ChevronDown className="w-4 h-4 text-gray-500" />
                </button>

                {showCategoryDropdown && (
                  <div className="absolute top-full left-0 mt-2 w-64 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-lg z-50 max-h-64 overflow-y-auto">
                    {categories.map((category) => (
                      <button
                        key={category.id}
                        onClick={() => {
                          setSelectedCategory(category.id);
                          setShowCategoryDropdown(false);
                        }}
                        className={`w-full text-left px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
                          selectedCategory === category.id ? 'bg-primary-50 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400' : 'text-gray-900 dark:text-white'
                        }`}
                      >
                        {category.name}
                      </button>
                    ))}
                  </div>
                )}
              </div>

              {/* Listing Type Filter */}
              <div className="relative">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    setShowListingTypeDropdown(!showListingTypeDropdown);
                  }}
                  className="flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                >
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {listingTypes.find(t => t.id === selectedListingType)?.name || 'Type'}
                  </span>
                  <ChevronDown className="w-4 h-4 text-gray-500" />
                </button>

                {showListingTypeDropdown && (
                  <div className="absolute top-full left-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-lg z-50">
                    {listingTypes.map((type) => (
                      <button
                        key={type.id}
                        onClick={() => {
                          setSelectedListingType(type.id);
                          setShowListingTypeDropdown(false);
                        }}
                        className={`w-full text-left px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors flex items-center space-x-2 ${
                          selectedListingType === type.id ? 'bg-primary-50 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400' : 'text-gray-900 dark:text-white'
                        }`}
                      >
                        <span>{type.icon}</span>
                        <span>{type.name}</span>
                      </button>
                    ))}
                  </div>
                )}
              </div>

              {/* Condition Filter */}
              <div className="relative">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    setShowFilterDropdown(!showFilterDropdown);
                  }}
                  className="flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                >
                  <Filter className="w-4 h-4 text-gray-500" />
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {conditions.find(c => c.id === selectedCondition)?.name || 'Condition'}
                  </span>
                  <ChevronDown className="w-4 h-4 text-gray-500" />
                </button>

                {showFilterDropdown && (
                  <div className="absolute top-full left-0 mt-2 w-64 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-lg z-50">
                    <div className="p-4 space-y-4">
                      <div>
                        <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-2">Condition</h3>
                        <div className="space-y-1">
                          {conditions.map((condition) => (
                            <button
                              key={condition.id}
                              onClick={() => {
                                setSelectedCondition(condition.id);
                                setShowFilterDropdown(false);
                              }}
                              className={`w-full text-left px-3 py-2 rounded-lg transition-colors ${
                                selectedCondition === condition.id
                                  ? 'bg-primary-50 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400'
                                  : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'
                              }`}
                            >
                              {condition.name}
                            </button>
                          ))}
                        </div>
                      </div>

                      <div>
                        <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-2">Price Range</h3>
                        <div className="space-y-1">
                          {priceRanges.map((range) => (
                            <button
                              key={range.id}
                              onClick={() => {
                                setSelectedPriceRange(range.id);
                                setShowFilterDropdown(false);
                              }}
                              className={`w-full text-left px-3 py-2 rounded-lg transition-colors ${
                                selectedPriceRange === range.id
                                  ? 'bg-primary-50 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400'
                                  : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'
                              }`}
                            >
                              {range.name}
                            </button>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Sort Dropdown */}
              <div className="relative ml-auto">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    setShowSortDropdown(!showSortDropdown);
                  }}
                  className="flex items-center space-x-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                >
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {sortOptions.find(s => s.id === selectedSort)?.name || 'Sort'}
                  </span>
                  <ChevronDown className="w-4 h-4 text-gray-500" />
                </button>

                {showSortDropdown && (
                  <div className="absolute top-full right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-lg z-50">
                    {sortOptions.map((option) => (
                      <button
                        key={option.id}
                        onClick={() => {
                          setSelectedSort(option.id);
                          setShowSortDropdown(false);
                        }}
                        className={`w-full text-left px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
                          selectedSort === option.id ? 'bg-primary-50 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400' : 'text-gray-900 dark:text-white'
                        }`}
                      >
                        {option.name}
                      </button>
                    ))}
                  </div>
                )}
              </div>

              {/* Layout Toggle */}
              <div className="flex items-center space-x-1 bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
                <button
                  onClick={() => setLayoutMode('grid')}
                  className={`p-2 rounded-md transition-colors ${
                    layoutMode === 'grid'
                      ? 'bg-white dark:bg-gray-600 text-primary-600 dark:text-primary-400 shadow-sm'
                      : 'text-gray-500 hover:text-gray-700 dark:hover:text-gray-300'
                  }`}
                >
                  <Grid className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setLayoutMode('list')}
                  className={`p-2 rounded-md transition-colors ${
                    layoutMode === 'list'
                      ? 'bg-white dark:bg-gray-600 text-primary-600 dark:text-primary-400 shadow-sm'
                      : 'text-gray-500 hover:text-gray-700 dark:hover:text-gray-300'
                  }`}
                >
                  <List className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setLayoutMode('compact')}
                  className={`p-2 rounded-md transition-colors ${
                    layoutMode === 'compact'
                      ? 'bg-white dark:bg-gray-600 text-primary-600 dark:text-primary-400 shadow-sm'
                      : 'text-gray-500 hover:text-gray-700 dark:hover:text-gray-300'
                  }`}
                >
                  <Grid3X3 className="w-4 h-4" />
                </button>
              </div>
            </div>

            {/* Search Results */}
            <div>
              {listingsLoading ? (
                <div className="flex items-center justify-center py-12">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
                </div>
              ) : sortedListings.length === 0 ? (
                <div className="text-center py-12">
                  <SearchIcon className="w-16 h-16 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">No results found</h3>
                  <p className="text-gray-600 dark:text-gray-400 mb-6">
                    Try adjusting your search terms or filters
                  </p>
                  <button
                    onClick={clearSearch}
                    className="inline-flex items-center px-6 py-3 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-lg transition-colors"
                  >
                    <X className="w-5 h-5 mr-2" />
                    Clear Search
                  </button>
                </div>
              ) : (
                <div className={`grid gap-3 sm:gap-4 md:gap-6 ${
                  layoutMode === 'grid'
                    ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3'
                    : layoutMode === 'compact'
                    ? 'grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6'
                    : 'grid-cols-1'
                }`}>
                  {sortedListings.map((listing) => (
                    <Link
                      key={listing.id}
                      to={`/listing/${listing.id}`}
                      className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 overflow-hidden hover:shadow-lg transition-all duration-200 hover:scale-[1.02] block"
                    >
                      <div className="aspect-square bg-gray-100 dark:bg-gray-700">
                        {listing.imageURLs && listing.imageURLs.length > 0 ? (
                          <img
                            src={listing.imageURLs[0]}
                            alt={listing.title}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center">
                            <ShoppingBag className="w-12 h-12 text-gray-400" />
                          </div>
                        )}
                      </div>
                      <div className="p-4">
                        <h3 className="font-semibold text-gray-900 dark:text-white mb-2 line-clamp-2">
                          {listing.title}
                        </h3>
                        <p className="text-2xl font-bold text-primary-600 dark:text-primary-400 mb-2">
                          ${listing.price}
                        </p>
                        <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
                          <span className="flex items-center">
                            <MapPin className="w-4 h-4 mr-1" />
                            {listing.university}
                          </span>
                          <span className="flex items-center">
                            <Eye className="w-4 h-4 mr-1" />
                            {(listing as any).views || 0}
                          </span>
                        </div>
                      </div>
                    </Link>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Search;
