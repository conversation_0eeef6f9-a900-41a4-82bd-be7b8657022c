import React, { useState, useRef } from 'react';
import { 
  MessageCircle, 
  Send, 
  Image, 
  Paperclip, 
  MoreVertical, 
  Search,
  ArrowLeft,
  Trash2,
  VolumeX,
  Flag,
  Phone,
  Video,
  Check,
  CheckCheck,
  Store,
  Users
} from 'lucide-react';


const MerchantMessages: React.FC = () => {
  const [selectedChat, setSelectedChat] = useState<number | null>(1);
  const [newMessage, setNewMessage] = useState('');
  const [showChatMenu, setShowChatMenu] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('merchants');
  const fileInputRef = useRef<HTMLInputElement>(null);

  // TODO: Fetch real merchant chat data from Firebase
  const merchantChats: any[] = [];

  const supportChats = [
    {
      id: 5,
      name: 'Hive Campus Support',
      avatar: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=100',
      lastMessage: 'Your account verification has been completed!',
      timestamp: '2h ago',
      unread: 1,
      online: true,
      businessType: 'Platform Support',
      verified: true
    }
  ];

  const messages = [
    {
      id: 1,
      senderId: 1,
      senderName: 'Tech Solutions Hub',
      content: 'Hi! I noticed you\'re doing great with electronics sales. Would you be interested in a bulk partnership?',
      timestamp: '10:30 AM',
      type: 'text',
      status: 'read'
    },
    {
      id: 2,
      senderId: 'me',
      senderName: 'You',
      content: 'That sounds interesting! What kind of partnership are you thinking about?',
      timestamp: '10:32 AM',
      type: 'text',
      status: 'read'
    },
    {
      id: 3,
      senderId: 1,
      senderName: 'Tech Solutions Hub',
      content: 'We could offer you wholesale prices on popular items, and you could refer customers to us for items you don\'t carry.',
      timestamp: '10:35 AM',
      type: 'text',
      status: 'read'
    },
    {
      id: 4,
      senderId: 'me',
      senderName: 'You',
      content: 'That could work well. Do you have a catalog of your current inventory?',
      timestamp: '10:37 AM',
      type: 'text',
      status: 'read'
    },
    {
      id: 5,
      senderId: 1,
      senderName: 'Tech Solutions Hub',
      content: 'Absolutely! I\'ll send you our latest catalog. We specialize in laptops, tablets, and accessories.',
      timestamp: '10:40 AM',
      type: 'text',
      status: 'read'
    },
    {
      id: 6,
      senderId: 'me',
      senderName: 'You',
      content: 'Perfect! Let\'s schedule a call to discuss the details.',
      timestamp: '10:42 AM',
      type: 'text',
      status: 'delivered'
    },
    {
      id: 7,
      senderId: 1,
      senderName: 'Tech Solutions Hub',
      content: 'Would you be interested in a bulk electronics partnership?',
      timestamp: '10:45 AM',
      type: 'text',
      status: 'sent'
    }
  ];

  const tabs = [
    { id: 'merchants', label: 'Merchants', count: merchantChats.length },
    { id: 'support', label: 'Support', count: supportChats.length }
  ];

  const selectedChatData = [...merchantChats, ...supportChats].find(chat => chat.id === selectedChat);
  const currentChats = activeTab === 'merchants' ? merchantChats : supportChats;
  const filteredChats = currentChats.filter(chat => 
    chat.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    chat.lastMessage.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleSendMessage = () => {
    if (newMessage.trim()) {
      console.log('Sending message:', newMessage);
      setNewMessage('');
    }
  };

  const handleFileUpload = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      console.log('Files selected:', files);
      // Handle file upload logic here
    }
  };

  const handleChatAction = (action: string) => {
    console.log(`Chat action: ${action}`);
    setShowChatMenu(false);
    
    switch (action) {
      case 'delete':
        // Handle delete chat
        break;
      case 'mute':
        // Handle mute chat
        break;
      case 'report':
        // Handle report chat
        break;
    }
  };

  const getMessageStatus = (status: string) => {
    switch (status) {
      case 'sent':
        return <Check className="w-4 h-4 text-gray-400" />;
      case 'delivered':
        return <CheckCheck className="w-4 h-4 text-gray-400" />;
      case 'read':
        return <CheckCheck className="w-4 h-4 text-accent-500" />;
      default:
        return null;
    }
  };

  return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden h-[calc(100vh-8rem)]">
            <div className="flex h-full">
              {/* Chat List Sidebar */}
              <div className={`w-full md:w-1/3 border-r border-gray-200 dark:border-gray-700 flex flex-col ${selectedChat ? 'hidden md:flex' : 'flex'}`}>
                {/* Header */}
                <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                  <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Messages</h1>
                  
                  {/* Tabs */}
                  <div className="flex space-x-1 mb-4 bg-gray-100 dark:bg-gray-700 p-1 rounded-xl">
                    {tabs.map((tab) => (
                      <button
                        key={tab.id}
                        onClick={() => setActiveTab(tab.id)}
                        className={`flex-1 flex items-center justify-center space-x-2 py-2 px-3 rounded-lg font-medium text-sm transition-all ${
                          activeTab === tab.id
                            ? 'bg-white dark:bg-gray-600 text-accent-600 dark:text-accent-400 shadow-sm'
                            : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200'
                        }`}
                      >
                        {tab.id === 'merchants' ? <Store className="w-4 h-4" /> : <Users className="w-4 h-4" />}
                        <span>{tab.label}</span>
                        <span className="bg-gray-200 dark:bg-gray-600 text-gray-600 dark:text-gray-400 px-2 py-1 rounded-full text-xs">
                          {tab.count}
                        </span>
                      </button>
                    ))}
                  </div>

                  <div className="relative">
                    <Search className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                    <input
                      type="text"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      placeholder="Search conversations..."
                      className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-accent-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                </div>

                {/* Chat List */}
                <div className="flex-1 overflow-y-auto">
                  {filteredChats.map((chat) => (
                    <div
                      key={chat.id}
                      onClick={() => setSelectedChat(chat.id)}
                      className={`p-4 border-b border-gray-100 dark:border-gray-700 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
                        selectedChat === chat.id ? 'bg-accent-50 dark:bg-accent-900/20 border-r-2 border-r-accent-500' : ''
                      }`}
                    >
                      <div className="flex items-center space-x-3">
                        <div className="relative">
                          <img
                            src={chat.avatar}
                            alt={chat.name}
                            className="w-12 h-12 rounded-full object-cover"
                          />
                          {chat.online && (
                            <div className="absolute bottom-0 right-0 w-3 h-3 bg-success-500 border-2 border-white dark:border-gray-800 rounded-full"></div>
                          )}
                          {chat.verified && (
                            <div className="absolute -top-1 -right-1 w-4 h-4 bg-accent-500 rounded-full flex items-center justify-center">
                              <Store className="w-2 h-2 text-white" />
                            </div>
                          )}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between mb-1">
                            <h3 className="font-semibold text-gray-900 dark:text-white truncate">
                              {chat.name}
                            </h3>
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                              {chat.timestamp}
                            </span>
                          </div>
                          <div className="flex items-center justify-between">
                            <p className="text-sm text-gray-600 dark:text-gray-400 truncate">
                              {chat.lastMessage}
                            </p>
                            {chat.unread > 0 && (
                              <span className="bg-accent-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                                {chat.unread}
                              </span>
                            )}
                          </div>
                          {/* Business Type */}
                          <div className="mt-1">
                            <span className="text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-full">
                              {chat.businessType}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Chat Window */}
              {selectedChat ? (
                <div className={`flex-1 flex flex-col ${selectedChat ? 'flex' : 'hidden md:flex'}`}>
                  {/* Chat Header */}
                  <div className="p-4 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <button
                          onClick={() => setSelectedChat(null)}
                          className="md:hidden p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                        >
                          <ArrowLeft className="w-5 h-5" />
                        </button>
                        <div className="relative">
                          <img
                            src={selectedChatData?.avatar}
                            alt={selectedChatData?.name}
                            className="w-10 h-10 rounded-full object-cover"
                          />
                          {selectedChatData?.online && (
                            <div className="absolute bottom-0 right-0 w-3 h-3 bg-success-500 border-2 border-white dark:border-gray-800 rounded-full"></div>
                          )}
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-900 dark:text-white">
                            {selectedChatData?.name}
                          </h3>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            {selectedChatData?.businessType} • {selectedChatData?.online ? 'Online' : 'Last seen recently'}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <button className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                          <Phone className="w-5 h-5" />
                        </button>
                        <button className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                          <Video className="w-5 h-5" />
                        </button>
                        <div className="relative">
                          <button
                            onClick={() => setShowChatMenu(!showChatMenu)}
                            className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                          >
                            <MoreVertical className="w-5 h-5" />
                          </button>
                          {showChatMenu && (
                            <div className="absolute right-0 top-full mt-2 w-48 bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-xl shadow-lg z-50">
                              <button
                                onClick={() => handleChatAction('mute')}
                                className="w-full text-left px-4 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 flex items-center space-x-3"
                              >
                                <VolumeX className="w-4 h-4" />
                                <span>Mute Chat</span>
                              </button>
                              <button
                                onClick={() => handleChatAction('report')}
                                className="w-full text-left px-4 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 flex items-center space-x-3"
                              >
                                <Flag className="w-4 h-4" />
                                <span>Report User</span>
                              </button>
                              <button
                                onClick={() => handleChatAction('delete')}
                                className="w-full text-left px-4 py-3 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 flex items-center space-x-3 rounded-b-xl"
                              >
                                <Trash2 className="w-4 h-4" />
                                <span>Delete Chat</span>
                              </button>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Messages */}
                  <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50 dark:bg-gray-900">
                    {messages.map((message) => (
                      <div
                        key={message.id}
                        className={`flex ${message.senderId === 'me' ? 'justify-end' : 'justify-start'}`}
                      >
                        <div
                          className={`max-w-xs lg:max-w-md px-4 py-2 rounded-2xl ${
                            message.senderId === 'me'
                              ? 'bg-accent-500 text-white'
                              : 'bg-white dark:bg-gray-800 text-gray-900 dark:text-white'
                          }`}
                        >
                          {message.type === 'image' ? (
                            <div className="space-y-2">
                              <img
                                src={message.content}
                                alt="Shared image"
                                className="rounded-lg max-w-full h-auto"
                              />
                            </div>
                          ) : (
                            <p className="text-sm">{message.content}</p>
                          )}
                          <div className={`flex items-center justify-between mt-1 ${
                            message.senderId === 'me' ? 'text-orange-100' : 'text-gray-500 dark:text-gray-400'
                          }`}>
                            <span className="text-xs">{message.timestamp}</span>
                            {message.senderId === 'me' && (
                              <div className="ml-2">
                                {getMessageStatus(message.status)}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Message Input */}
                  <div className="sticky bottom-0 p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 z-20">
                    <div className="flex items-center space-x-3">
                      <button
                        onClick={handleFileUpload}
                        className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                      >
                        <Paperclip className="w-5 h-5" />
                      </button>
                      <button
                        onClick={handleFileUpload}
                        className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                      >
                        <Image className="w-5 h-5" />
                      </button>
                      <div className="flex-1 relative">
                        <input
                          type="text"
                          value={newMessage}
                          onChange={(e) => setNewMessage(e.target.value)}
                          onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                          placeholder="Type a message..."
                          className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-accent-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                        />
                      </div>
                      <button
                        onClick={handleSendMessage}
                        disabled={!newMessage.trim()}
                        className="p-3 bg-accent-500 text-white rounded-xl hover:bg-accent-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                      >
                        <Send className="w-5 h-5" />
                      </button>
                    </div>
                    <input
                      ref={fileInputRef}
                      type="file"
                      multiple
                      accept="image/*,video/*,.pdf,.doc,.docx"
                      onChange={handleFileChange}
                      className="hidden"
                    />
                  </div>
                </div>
              ) : (
                <div className="hidden md:flex flex-1 items-center justify-center bg-gray-50 dark:bg-gray-900">
                  <div className="text-center">
                    <MessageCircle className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                      Select a conversation
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400">
                      Choose a chat from the sidebar to start messaging
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
  );
};

export default MerchantMessages;