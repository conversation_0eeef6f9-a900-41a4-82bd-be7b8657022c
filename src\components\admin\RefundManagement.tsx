import React, { useState, useEffect } from 'react';
import {
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  XCircle,
  DollarSign,
  User,
  Calendar,
  MessageSquare
} from 'lucide-react';
import { AdminStripeService } from '../../services/adminStripeService';
import { AdminDataService } from '../../services/AdminDataService';
import { useAuth } from '../../hooks/useAuth';
import { logAdminAction } from '../../utils/adminAuth';
import toast from 'react-hot-toast';

interface RefundableTransaction {
  id: string;
  buyerId: string;
  buyerName: string;
  sellerId: string;
  sellerName: string;
  listingTitle: string;
  totalAmount: number;
  status: string;
  createdAt: any;
  stripePaymentIntentId?: string;
}

const RefundManagement: React.FC = () => {
  const { userProfile } = useAuth();
  const [refundableTransactions, setRefundableTransactions] = useState<RefundableTransaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [processingRefunds, setProcessingRefunds] = useState<Set<string>>(new Set());
  const [showRefundModal, setShowRefundModal] = useState(false);
  const [selectedTransaction, setSelectedTransaction] = useState<RefundableTransaction | null>(null);
  const [refundReason, setRefundReason] = useState('');

  useEffect(() => {
    fetchRefundableTransactions();
  }, []);

  const fetchRefundableTransactions = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Get completed transactions that can be refunded
      const transactions = await AdminDataService.getTransactions({
        status: 'completed',
        pageSize: 50
      });
      
      setRefundableTransactions(transactions);
    } catch (err) {
      console.error('Error fetching refundable transactions:', err);
      setError('Failed to load refundable transactions');
    } finally {
      setLoading(false);
    }
  };

  const handleRefundRequest = (transaction: RefundableTransaction) => {
    setSelectedTransaction(transaction);
    setRefundReason('');
    setShowRefundModal(true);
  };

  const processRefund = async () => {
    if (!selectedTransaction || !refundReason.trim()) {
      toast.error('Please provide a reason for the refund');
      return;
    }

    try {
      setProcessingRefunds(prev => new Set(prev).add(selectedTransaction.id));
      
      if (userProfile) {
        await logAdminAction(userProfile, 'transaction_refund', { 
          transactionId: selectedTransaction.id,
          reason: refundReason.trim()
        });
      }

      await AdminStripeService.refundTransaction(
        selectedTransaction.id, 
        undefined, 
        refundReason.trim()
      );
      
      toast.success('✅ Refund processed successfully!');
      setShowRefundModal(false);
      setSelectedTransaction(null);
      setRefundReason('');
      await fetchRefundableTransactions(); // Refresh the list
      
    } catch (err) {
      console.error('Error processing refund:', err);
      toast.error('❌ Failed to process refund. Please try again.');
    } finally {
      setProcessingRefunds(prev => {
        const newSet = new Set(prev);
        newSet.delete(selectedTransaction.id);
        return newSet;
      });
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatDate = (date: any) => {
    if (!date) return 'N/A';
    const dateObj = date.toDate ? date.toDate() : new Date(date);
    return dateObj.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <RefreshCw className="h-6 w-6 text-orange-600 dark:text-orange-400 mr-2" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Refundable Transactions ({refundableTransactions.length})
              </h3>
            </div>
            <button
              onClick={fetchRefundableTransactions}
              disabled={loading}
              className="inline-flex items-center px-3 py-1 text-sm font-medium text-gray-700 dark:text-gray-200 bg-gray-100 dark:bg-gray-700 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 disabled:opacity-50"
            >
              <RefreshCw className={`h-4 w-4 mr-1 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </button>
          </div>
        </div>

        {error && (
          <div className="px-6 py-4 bg-red-50 dark:bg-red-900/20 border-b border-red-200 dark:border-red-800">
            <div className="flex items-center">
              <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400 mr-2" />
              <p className="text-red-800 dark:text-red-200">{error}</p>
            </div>
          </div>
        )}

        <div className="divide-y divide-gray-200 dark:divide-gray-700">
          {refundableTransactions.length === 0 ? (
            <div className="px-6 py-12 text-center">
              <CheckCircle className="mx-auto h-12 w-12 text-green-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No refundable transactions</h3>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                All transactions are either pending or have already been refunded.
              </p>
            </div>
          ) : (
            refundableTransactions.map((transaction) => (
              <div key={transaction.id} className="px-6 py-4 hover:bg-gray-50 dark:hover:bg-gray-700/50">
                <div className="flex items-center justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        <div className="w-10 h-10 bg-orange-100 dark:bg-orange-900/20 rounded-full flex items-center justify-center">
                          <User className="h-5 w-5 text-orange-600 dark:text-orange-400" />
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2">
                          <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                            {transaction.buyerName}
                          </p>
                          <span className="text-xs text-gray-500 dark:text-gray-400">purchased</span>
                          <p className="text-sm text-gray-600 dark:text-gray-300 truncate">
                            {transaction.listingTitle}
                          </p>
                        </div>
                        <div className="flex items-center space-x-4 mt-1">
                          <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                            <Calendar className="h-3 w-3 mr-1" />
                            {formatDate(transaction.createdAt)}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            Order: {transaction.id.substring(0, 8)}...
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-4">
                    <div className="text-right">
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {formatCurrency(transaction.totalAmount)}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        Status: {transaction.status}
                      </div>
                    </div>
                    
                    <button
                      onClick={() => handleRefundRequest(transaction)}
                      disabled={processingRefunds.has(transaction.id)}
                      className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 disabled:bg-orange-400 rounded-md transition-colors"
                    >
                      {processingRefunds.has(transaction.id) ? (
                        <>
                          <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                          Processing...
                        </>
                      ) : (
                        <>
                          <RefreshCw className="h-4 w-4 mr-2" />
                          Issue Refund
                        </>
                      )}
                    </button>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Refund Modal */}
      {showRefundModal && selectedTransaction && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Process Refund
            </h3>
            <div className="mb-4">
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                Transaction: {selectedTransaction.listingTitle}
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                Amount: {formatCurrency(selectedTransaction.totalAmount)}
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                Buyer: {selectedTransaction.buyerName}
              </p>
            </div>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Reason for refund *
              </label>
              <textarea
                value={refundReason}
                onChange={(e) => setRefundReason(e.target.value)}
                placeholder="Enter the reason for this refund..."
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 dark:bg-gray-700 dark:text-white"
                rows={3}
                required
              />
            </div>
            <div className="flex space-x-3">
              <button
                onClick={() => {
                  setShowRefundModal(false);
                  setSelectedTransaction(null);
                  setRefundReason('');
                }}
                className="flex-1 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 bg-gray-100 dark:bg-gray-700 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600"
              >
                Cancel
              </button>
              <button
                onClick={processRefund}
                disabled={!refundReason.trim() || processingRefunds.has(selectedTransaction.id)}
                className="flex-1 px-4 py-2 text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 disabled:bg-orange-400 rounded-md"
              >
                {processingRefunds.has(selectedTransaction.id) ? 'Processing...' : 'Process Refund'}
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default RefundManagement;
