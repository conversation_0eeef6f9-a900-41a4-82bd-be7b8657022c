import { httpsCallable } from 'firebase/functions';
import { functions } from '../firebase/config';

export interface SystemHealthData {
  services: {
    stripe: { status: string; latency?: string; error?: string };
    firebaseAuth: { status: string; latency?: string; error?: string };
    fcm: { status: string; note?: string; error?: string };
  };
  dataSync: {
    usersCount: number;
    ordersCount: number;
    listingsCount: number;
    notificationsCount: number;
    firestoreLatency: string;
  };
  integrity: {
    usersWithoutEmail: number;
    listingsWithoutPrice: number;
    listingsWithoutImage: number;
    listingsWithoutSeller: number;
    stuckTransactions: number;
    refundsWithHeldEscrow: number;
    totalIssues: number;
  };
  errors: {
    last24Hours: number;
    errorsByType: Record<string, number>;
    recentErrors: any[];
    criticalErrors: number;
  };
  latency: {
    firestore: number;
  };
  timestamp: any;
}

export interface HealthAlert {
  id: string;
  type: 'critical' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: Date;
  resolved: boolean;
}

export class AdminHealthService {
  private static healthData: SystemHealthData | null = null;
  private static lastCheck: Date | null = null;
  private static alerts: HealthAlert[] = [];

  /**
   * Perform comprehensive system health check
   */
  static async performHealthCheck(): Promise<SystemHealthData> {
    try {
      const adminHealthCheck = httpsCallable(functions, 'adminHealthCheck');
      const result = await adminHealthCheck();
      const data = result.data as { success: boolean; data: SystemHealthData };
      
      if (data.success) {
        this.healthData = data.data;
        this.lastCheck = new Date();
        this.analyzeHealthData(data.data);
        return data.data;
      } else {
        throw new Error('Health check failed');
      }
    } catch (error) {
      console.error('Health check error:', error);
      throw error;
    }
  }

  /**
   * Get cached health data
   */
  static getCachedHealthData(): SystemHealthData | null {
    return this.healthData;
  }

  /**
   * Get last check timestamp
   */
  static getLastCheckTime(): Date | null {
    return this.lastCheck;
  }

  /**
   * Analyze health data and generate alerts
   */
  private static analyzeHealthData(data: SystemHealthData): void {
    const newAlerts: HealthAlert[] = [];

    // Check for critical service failures
    Object.entries(data.services).forEach(([serviceName, service]) => {
      if (service.status === 'error') {
        newAlerts.push({
          id: `service-${serviceName}-${Date.now()}`,
          type: 'critical',
          title: `${serviceName} Service Down`,
          message: `${serviceName} service is experiencing issues: ${service.error}`,
          timestamp: new Date(),
          resolved: false
        });
      }
    });

    // Check for high error rates
    if (data.errors.criticalErrors > 10) {
      newAlerts.push({
        id: `high-error-rate-${Date.now()}`,
        type: 'critical',
        title: 'High Error Rate Detected',
        message: `${data.errors.criticalErrors} critical errors in the last 24 hours`,
        timestamp: new Date(),
        resolved: false
      });
    }

    // Check for stuck transactions
    if (data.integrity.stuckTransactions > 5) {
      newAlerts.push({
        id: `stuck-transactions-${Date.now()}`,
        type: 'warning',
        title: 'Stuck Transactions Detected',
        message: `${data.integrity.stuckTransactions} transactions have been pending for over 48 hours`,
        timestamp: new Date(),
        resolved: false
      });
    }

    // Check for data integrity issues
    if (data.integrity.totalIssues > 20) {
      newAlerts.push({
        id: `data-integrity-${Date.now()}`,
        type: 'warning',
        title: 'Data Integrity Issues',
        message: `${data.integrity.totalIssues} data integrity issues detected`,
        timestamp: new Date(),
        resolved: false
      });
    }

    // Check for high latency
    if (data.latency.firestore > 2000) {
      newAlerts.push({
        id: `high-latency-${Date.now()}`,
        type: 'warning',
        title: 'High Database Latency',
        message: `Firestore latency is ${data.latency.firestore}ms (above 2000ms threshold)`,
        timestamp: new Date(),
        resolved: false
      });
    }

    // Add new alerts
    this.alerts = [...newAlerts, ...this.alerts.slice(0, 50)]; // Keep last 50 alerts
  }

  /**
   * Get current alerts
   */
  static getAlerts(): HealthAlert[] {
    return this.alerts;
  }

  /**
   * Get unresolved alerts
   */
  static getUnresolvedAlerts(): HealthAlert[] {
    return this.alerts.filter(alert => !alert.resolved);
  }

  /**
   * Mark alert as resolved
   */
  static resolveAlert(alertId: string): void {
    this.alerts = this.alerts.map(alert => 
      alert.id === alertId ? { ...alert, resolved: true } : alert
    );
  }

  /**
   * Get system health score (0-100)
   */
  static getHealthScore(data?: SystemHealthData): number {
    const healthData = data || this.healthData;
    if (!healthData) return 0;

    let score = 100;

    // Deduct points for service failures
    Object.values(healthData.services).forEach(service => {
      if (service.status === 'error') score -= 20;
    });

    // Deduct points for critical errors
    score -= Math.min(healthData.errors.criticalErrors * 2, 30);

    // Deduct points for data integrity issues
    score -= Math.min(healthData.integrity.totalIssues, 20);

    // Deduct points for high latency
    if (healthData.latency.firestore > 2000) score -= 10;

    return Math.max(score, 0);
  }

  /**
   * Get health status based on score
   */
  static getHealthStatus(score?: number): 'healthy' | 'warning' | 'critical' {
    const healthScore = score || this.getHealthScore();
    
    if (healthScore >= 80) return 'healthy';
    if (healthScore >= 60) return 'warning';
    return 'critical';
  }

  /**
   * Export health data as CSV
   */
  static exportHealthDataAsCSV(data: SystemHealthData): string {
    const timestamp = new Date().toISOString();
    
    let csv = 'Hive Campus - System Health Report\n';
    csv += `Generated: ${timestamp}\n\n`;
    
    // Services
    csv += 'Service,Status,Latency,Error\n';
    Object.entries(data.services).forEach(([name, service]) => {
      csv += `${name},${service.status},${service.latency || 'N/A'},${service.error || 'N/A'}\n`;
    });
    
    csv += '\nData Sync\n';
    csv += 'Metric,Value\n';
    Object.entries(data.dataSync).forEach(([key, value]) => {
      csv += `${key},${value}\n`;
    });
    
    csv += '\nDatabase Integrity\n';
    csv += 'Issue,Count\n';
    Object.entries(data.integrity).forEach(([key, value]) => {
      csv += `${key},${value}\n`;
    });
    
    csv += '\nError Summary\n';
    csv += 'Metric,Value\n';
    csv += `Total Errors (24h),${data.errors.last24Hours}\n`;
    csv += `Critical Errors,${data.errors.criticalErrors}\n`;
    
    return csv;
  }

  /**
   * Get health trends (mock data for now - would be real historical data in production)
   */
  static getHealthTrends(): Array<{ timestamp: string; score: number; status: string }> {
    const trends = [];
    const now = new Date();
    
    for (let i = 23; i >= 0; i--) {
      const timestamp = new Date(now.getTime() - i * 60 * 60 * 1000);
      const score = Math.floor(Math.random() * 20) + 80; // Mock data between 80-100
      const status = this.getHealthStatus(score);
      
      trends.push({
        timestamp: timestamp.toISOString(),
        score,
        status
      });
    }
    
    return trends;
  }

  /**
   * Schedule automatic health checks
   */
  static startAutoHealthCheck(intervalMinutes: number = 10): () => void {
    const interval = setInterval(() => {
      this.performHealthCheck().catch(console.error);
    }, intervalMinutes * 60 * 1000);

    // Return cleanup function
    return () => clearInterval(interval);
  }
}

export default AdminHealthService;
