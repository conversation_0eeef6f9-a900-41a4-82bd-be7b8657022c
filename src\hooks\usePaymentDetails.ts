import { useState, useCallback } from 'react';
import { httpsCallable } from 'firebase/functions';
import { functions } from '../firebase/config';

export interface PaymentMethodDetails {
  type: string;
  card?: {
    brand: string;
    last4: string;
    exp_month: number;
    exp_year: number;
    funding: string;
  };
}

export interface ChargeDetails {
  id: string;
  amount: number;
  currency: string;
  status: string;
  created: number;
  paid: boolean;
  refunded: boolean;
  amount_refunded: number;
  receipt_url?: string;
  billing_details?: {
    email?: string;
    name?: string;
    phone?: string;
    address?: any;
  };
  outcome?: {
    network_status: string;
    reason?: string;
    risk_level: string;
    seller_message: string;
    type: string;
  };
}

export interface StripePaymentIntentDetails {
  id: string;
  amount: number;
  currency: string;
  status: string;
  created: number;
  description?: string;
  receipt_email?: string;
  payment_method_types: string[];
}

export interface StripeSessionDetails {
  id: string;
  payment_status: string;
  status: string;
  amount_total?: number;
  currency?: string;
  created: number;
  expires_at?: number;
  customer_email?: string;
  customer_name?: string;
  payment_method_types: string[];
}

export interface PaymentDetails {
  orderId: string;
  orderAmount: number;
  commissionAmount: number;
  sellerAmount: number;
  walletAmountUsed: number;
  status: string;
  createdAt: any;
  paymentCompletedAt?: any;
  stripePaymentIntent?: StripePaymentIntentDetails;
  paymentMethod?: PaymentMethodDetails;
  charge?: ChargeDetails;
  stripeSession?: StripeSessionDetails;
  stripeError?: string;
  sessionError?: string;
}

export const usePaymentDetails = () => {
  const [paymentDetails, setPaymentDetails] = useState<PaymentDetails | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchPaymentDetails = useCallback(async (orderId: string) => {
    if (!orderId) {
      setError('Order ID is required');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Try to call the function
      const getPaymentDetails = httpsCallable(functions, 'getPaymentDetails');
      const result = await getPaymentDetails({ orderId });

      const details = result.data as PaymentDetails;
      setPaymentDetails(details);
    } catch (err) {
      console.error('Error fetching payment details:', err);

      // Temporary fallback: If function doesn't exist, try to get order data directly
      if (err instanceof Error && err.message.includes('internal')) {
        try {
          console.log('Falling back to direct Firestore access...');
          const { doc, getDoc } = await import('firebase/firestore');
          const { db } = await import('../firebase/config');

          const orderRef = doc(db, 'orders', orderId);
          const orderSnap = await getDoc(orderRef);

          if (orderSnap.exists()) {
            const orderData = orderSnap.data();
            const fallbackDetails: PaymentDetails = {
              orderId,
              amount: orderData.totalAmount || 0,
              itemPrice: orderData.itemPrice || 0,
              shippingCost: orderData.shippingCost || 0,
              walletBalanceUsed: orderData.walletBalanceUsed || 0,
              originalTotal: orderData.originalTotal || 0,
              status: orderData.status || 'unknown',
              createdAt: orderData.createdAt,
              paymentCompletedAt: orderData.paymentCompletedAt,
              secretCode: orderData.secretCode,
              stripeSessionId: orderData.stripeSessionId,
              stripePaymentIntentId: orderData.stripePaymentIntentId
            };
            setPaymentDetails(fallbackDetails);
            return;
          }
        } catch (fallbackErr) {
          console.error('Fallback also failed:', fallbackErr);
        }
      }

      setError(err instanceof Error ? err.message : 'Failed to fetch payment details');
      setPaymentDetails(null);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const clearPaymentDetails = useCallback(() => {
    setPaymentDetails(null);
    setError(null);
  }, []);

  return {
    paymentDetails,
    isLoading,
    error,
    fetchPaymentDetails,
    clearPaymentDetails,
  };
};
