{"timestamp": "2025-07-24T14:21:08.177Z", "overallScore": 81, "passed": 26, "failed": 3, "warnings": 3, "categories": {"firebase-rules": {"tests": [{"test": "No unsafe global read/write rules", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-24T14:21:08.341Z"}, {"test": "Authentication checks implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-24T14:21:08.341Z"}, {"test": "Role-based access control implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-24T14:21:08.341Z"}, {"test": "User ownership validation present", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-24T14:21:08.341Z"}, {"test": "Input validation in security rules", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-24T14:21:08.342Z"}], "score": 100}, "secrets": {"tests": [{"test": "No hardcoded secrets in source code", "status": "FAIL", "severity": "Critical", "details": null, "timestamp": "2025-07-24T14:21:13.101Z"}, {"test": "All required environment variables present", "status": "FAIL", "severity": "Critical", "details": "Missing: VITE_FIREBASE_API_KEY, VITE_FIREBASE_AUTH_DOMAIN, VITE_FIREBASE_PROJECT_ID, VITE_FIREBASE_STORAGE_BUCKET, VITE_FIREBASE_MESSAGING_SENDER_ID, VITE_FIREBASE_APP_ID, VITE_STRIPE_PUBLISHABLE_KEY", "timestamp": "2025-07-24T14:21:13.101Z"}, {"test": "No .env files committed to repository", "status": "FAIL", "severity": "High", "details": "Found: .env, .env.production", "timestamp": "2025-07-24T14:21:13.102Z"}], "score": 0}, "dependencies": {"tests": [{"test": "No critical vulnerabilities", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-24T14:21:16.021Z"}, {"test": "No high-severity vulnerabilities", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-24T14:21:16.021Z"}, {"test": "Moderate vulnerabilities under control", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-24T14:21:16.021Z"}, {"test": "Some dependencies may be outdated", "status": "WARN", "severity": "Low", "details": null, "timestamp": "2025-07-24T14:21:26.817Z"}], "score": 75}, "client-security": {"tests": [{"test": "Security utilities implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-24T14:21:26.818Z"}, {"test": "Input sanitization implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-24T14:21:26.818Z"}, {"test": "HTTPS enforcement implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-24T14:21:26.819Z"}, {"test": "Client-side rate limiting implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-24T14:21:26.819Z"}, {"test": "Security initialization in main app", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-24T14:21:26.819Z"}], "score": 100}, "network-security": {"tests": [{"test": "Security headers configured", "status": "WARN", "severity": "Medium", "details": null, "timestamp": "2025-07-24T14:21:26.925Z"}, {"test": "HTTPS configuration present", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-24T14:21:26.925Z"}, {"test": "Firebase hosting security headers configured", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-24T14:21:27.269Z"}, {"test": "HSTS header configured", "status": "WARN", "severity": "Medium", "details": null, "timestamp": "2025-07-24T14:21:27.269Z"}], "score": 50}, "data-privacy": {"tests": [{"test": "Privacy policy page exists", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-24T14:21:27.270Z"}, {"test": "Terms and conditions page exists", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-24T14:21:27.271Z"}, {"test": "Consent management implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-24T14:21:27.322Z"}, {"test": "Data deletion policies in Firebase rules", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-24T14:21:27.323Z"}], "score": 100}, "csp": {"tests": [{"test": "CSP manager implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-24T14:21:27.324Z"}, {"test": "Nonce generation for inline scripts", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-24T14:21:27.324Z"}, {"test": "CSP meta tag in HTML", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-24T14:21:27.525Z"}], "score": 100}, "authentication": {"tests": [{"test": "Authentication context implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-24T14:21:27.526Z"}, {"test": "Protected route component implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-24T14:21:27.526Z"}, {"test": "Role-based access control implemented", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-24T14:21:27.527Z"}, {"test": "Firebase Auth domain configured", "status": "PASS", "severity": "Info", "details": null, "timestamp": "2025-07-24T14:21:27.527Z"}], "score": 100}}}