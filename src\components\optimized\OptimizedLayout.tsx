import React, { memo, useMemo } from 'react';
import { usePerformance } from '../../hooks/usePerformance';
import { useAuth } from '../../hooks/useAuth';

interface OptimizedLayoutProps {
  children: React.ReactNode;
  className?: string;
}

// Memoized header component
const Header = memo(({ 
  isAdmin, 
  isMerchant, 
  currentUser, 
  userProfile: _userProfile 
}: { 
  isAdmin: boolean;
  isMerchant: boolean;
  currentUser: Record<string, unknown>;
  userProfile: Record<string, unknown>;
}) => {
  usePerformance('Header');
  
  const headerContent = useMemo(() => {
    const getLogoConfig = () => {
      if (isAdmin) {
        return {
          icon: 'shield',
          bgColor: 'from-red-500 to-red-600',
          badge: 'Admin'
        };
      }
      if (isMerchant) {
        return {
          icon: 'store',
          bgColor: 'from-accent-500 to-orange-500',
          badge: 'Partner'
        };
      }
      return {
        icon: 'logo',
        bgColor: '',
        badge: null
      };
    };

    return getLogoConfig();
  }, [isAdmin, isMerchant]);

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-white/95 dark:bg-gray-800/95 backdrop-blur-md shadow-sm border-b border-gray-200 dark:border-gray-700">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo and branding - memoized */}
          <div className="flex items-center space-x-2">
            {headerContent.icon === 'logo' ? (
              <img
                src="/hive-campus-logo.svg"
                alt="Hive Campus Logo"
                className="w-8 h-8"
                loading="eager"
                decoding="sync"
              />
            ) : (
              <div className={`w-8 h-8 bg-gradient-to-r ${headerContent.bgColor} rounded-xl flex items-center justify-center`}>
                <span className="w-5 h-5 text-white">
                  {headerContent.icon === 'shield' ? '🛡️' : '🏪'}
                </span>
              </div>
            )}
            <span className="text-xl font-bold text-gray-900 dark:text-white">Hive Campus</span>
            {headerContent.badge && (
              <span className={`text-sm font-medium ${
                isAdmin ? 'text-red-600 dark:text-red-400' : 'text-accent-600 dark:text-accent-400'
              }`}>
                {headerContent.badge}
              </span>
            )}
          </div>
          
          {/* User actions - lazy loaded */}
          <div className="flex items-center">
            {currentUser ? (
              <div className="user-dropdown-placeholder">
                {/* User dropdown will be loaded here */}
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <a 
                  href="/login" 
                  className="px-4 py-2 text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors"
                >
                  Sign In
                </a>
                <a 
                  href="/signup" 
                  className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
                >
                  Sign Up
                </a>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
});

Header.displayName = 'Header';

// Memoized navigation component
interface NavigationItem {
  icon: React.ComponentType<{ className?: string }>;
  label: string;
  path: string;
}

interface LocationState {
  pathname: string;
}

const Navigation = memo(({ 
  navItems, 
  location, 
  isAdmin, 
  isMerchant 
}: { 
  navItems: NavigationItem[];
  location: LocationState;
  isAdmin: boolean;
  isMerchant: boolean;
}) => {
  usePerformance('Navigation');
  
  const getActiveStyle = useMemo(() => {
    return (isActive: boolean) => {
      if (!isActive) return 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700';
      
      if (isAdmin) return 'bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400';
      if (isMerchant) return 'bg-accent-50 dark:bg-accent-900/20 text-accent-600 dark:text-accent-400';
      return 'bg-primary-50 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400';
    };
  }, [isAdmin, isMerchant]);

  return (
    <>
      {/* Mobile Navigation */}
      <nav className="fixed bottom-0 left-0 right-0 z-40 bg-white/95 dark:bg-gray-800/95 backdrop-blur-md border-t border-gray-200 dark:border-gray-700 md:hidden safe-area-inset-bottom">
        <div className="flex justify-around py-2">
          {navItems.slice(0, 5).map((item) => {
            const isActive = location.pathname === item.path;
            return (
              <a
                key={item.path}
                href={item.path}
                className={`flex flex-col items-center p-2 relative ${getActiveStyle(isActive)}`}
              >
                <item.icon className="w-6 h-6" />
                <span className="text-xs mt-1">{item.label}</span>
              </a>
            );
          })}
        </div>
      </nav>

      {/* Desktop Navigation */}
      <aside className="hidden md:fixed md:left-0 md:top-16 md:bottom-0 md:w-64 md:bg-white md:dark:bg-gray-800 md:border-r md:border-gray-200 md:dark:border-gray-700 md:flex md:flex-col">
        <nav className="flex-1 p-4 space-y-2">
          {navItems.map((item) => {
            const isActive = location.pathname === item.path;
            return (
              <a
                key={item.path}
                href={item.path}
                className={`flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-200 relative ${getActiveStyle(isActive)}`}
              >
                <item.icon className="w-5 h-5" />
                <span className="font-medium">{item.label}</span>
              </a>
            );
          })}
        </nav>
      </aside>
    </>
  );
});

Navigation.displayName = 'Navigation';

// Main optimized layout component
const OptimizedLayout: React.FC<OptimizedLayoutProps> = ({ children, className = '' }) => {
  usePerformance('OptimizedLayout');
  
  const { currentUser, userProfile, isLoading, isAdmin, userRole } = useAuth();
  const isMerchant = userRole === 'merchant';

  // Memoize navigation items to prevent recreation on every render
  const navItems = useMemo(() => {
    if (isAdmin) {
      return [
        { icon: React.lazy(() => import('lucide-react').then(m => ({ default: m.BarChart3 }))), label: 'Dashboard', path: '/admin/dashboard-new' },
        { icon: React.lazy(() => import('lucide-react').then(m => ({ default: m.Users }))), label: 'Users', path: '/admin/users' },
        { icon: React.lazy(() => import('lucide-react').then(m => ({ default: m.Package }))), label: 'Listings', path: '/admin/listings' },
        { icon: React.lazy(() => import('lucide-react').then(m => ({ default: m.Flag }))), label: 'Reports', path: '/admin/reports' },
        { icon: React.lazy(() => import('lucide-react').then(m => ({ default: m.Shield }))), label: 'Security', path: '/admin/security' },
        { icon: React.lazy(() => import('lucide-react').then(m => ({ default: m.Activity }))), label: 'ReeFlex', path: '/admin/reeflex' },
        { icon: React.lazy(() => import('lucide-react').then(m => ({ default: m.Database }))), label: 'Analytics', path: '/admin/analytics' },
        { icon: React.lazy(() => import('lucide-react').then(m => ({ default: m.Settings }))), label: 'Settings', path: '/admin/settings' },
      ];
    } else if (isMerchant) {
      return [
        { icon: React.lazy(() => import('lucide-react').then(m => ({ default: m.BarChart3 }))), label: 'Dashboard', path: '/merchant/dashboard' },
        { icon: React.lazy(() => import('lucide-react').then(m => ({ default: m.Package }))), label: 'Products', path: '/merchant/products' },
        { icon: React.lazy(() => import('lucide-react').then(m => ({ default: m.MessageCircle }))), label: 'Messages', path: '/merchant/messages' },
        { icon: React.lazy(() => import('lucide-react').then(m => ({ default: m.User }))), label: 'Profile', path: '/merchant/profile' },
      ];
    } else {
      return [
        { icon: React.lazy(() => import('lucide-react').then(m => ({ default: m.Home }))), label: 'Home', path: '/home' },
        { icon: React.lazy(() => import('lucide-react').then(m => ({ default: m.Search }))), label: 'Search', path: '/search' },
        { icon: React.lazy(() => import('lucide-react').then(m => ({ default: m.Plus }))), label: 'Sell', path: '/add-listing' },
        { icon: React.lazy(() => import('lucide-react').then(m => ({ default: m.Wallet }))), label: 'Wallet', path: '/wallet' },
        { icon: React.lazy(() => import('lucide-react').then(m => ({ default: m.User }))), label: 'Profile', path: '/profile' },
      ];
    }
  }, [isAdmin, isMerchant]);

  // Memoize location object
  const location = useMemo(() => ({
    pathname: window.location.pathname
  }), []);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col ${className}`}>
      <Header 
        isAdmin={isAdmin}
        isMerchant={isMerchant}
        currentUser={currentUser}
        userProfile={userProfile}
      />
      
      <main className="flex-1 pb-20 md:pb-0 md:ml-64 pt-16">
        {children}
      </main>
      
      <Navigation 
        navItems={navItems}
        location={location}
        isAdmin={isAdmin}
        isMerchant={isMerchant}
      />
    </div>
  );
};

export default memo(OptimizedLayout);