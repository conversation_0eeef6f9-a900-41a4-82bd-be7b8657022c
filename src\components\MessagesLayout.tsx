import React from 'react';
import { Home, Plus, User, Wallet, MessageCircle, Shield, BarChart3, Users, Package, Flag, Activity, Database, Settings, Search } from 'lucide-react';
import { Link, useLocation } from 'react-router-dom';
import FloatingChat from './FloatingChat';
import { useAuth } from '../hooks/useAuth';

interface MessagesLayoutProps {
  children: React.ReactNode;
}

const MessagesLayout: React.FC<MessagesLayoutProps> = ({ children }) => {
  const location = useLocation();
  const { currentUser, userRole } = useAuth();
  
  const isAdmin = userRole === 'admin';
  const isMerchant = userRole === 'merchant';

  // Navigation items based on user role
  const getNavItems = () => {
    if (isAdmin) {
      return [
        { path: '/admin/dashboard-new', icon: BarChart3, label: 'Dashboard' },
        { path: '/admin/users', icon: Users, label: 'Users' },
        { path: '/admin/listings', icon: Package, label: 'Listings' },
        { path: '/admin/reports', icon: Flag, label: 'Reports' },
        { path: '/admin/analytics', icon: Activity, label: 'Analytics' },
        { path: '/admin/data', icon: Database, label: 'Data' },
        { path: '/admin/settings', icon: Settings, label: 'Settings' }
      ];
    } else if (isMerchant) {
      return [
        { path: '/merchant/dashboard', icon: Home, label: 'Dashboard' },
        { path: '/merchant/messages', icon: MessageCircle, label: 'Messages' },
        { path: '/merchant/profile', icon: User, label: 'Profile' },
        { path: '/merchant/settings', icon: Settings, label: 'Settings' }
      ];
    } else {
      return [
        { path: '/home', icon: Home, label: 'Home' },
        { path: '/search', icon: Search, label: 'Search' },
        { path: '/add-listing', icon: Plus, label: 'Sell' },
        { path: '/messages', icon: MessageCircle, label: 'Messages' },
        { path: '/profile', icon: User, label: 'Profile' },
        { path: '/wallet', icon: Wallet, label: 'Wallet' }
      ];
    }
  };

  const navItems = getNavItems();

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col">
      {/* Main Content - No Header */}
      <main className="flex-1 pb-20 md:pb-0 md:ml-64">
        {children}
      </main>

      {/* Bottom Navigation (Mobile) */}
      <nav className="fixed bottom-0 left-0 right-0 z-40 bg-white/95 dark:bg-gray-800/95 backdrop-blur-md border-t border-gray-200 dark:border-gray-700 md:hidden safe-area-inset-bottom">
        <div className="flex justify-around py-2">
          {navItems.slice(0, 5).map((item) => {
            const Icon = item.icon;
            const isActive = location.pathname === item.path;
            const hasNotification = false; // No notifications for current nav items
            
            return (
              <Link
                key={item.path}
                to={item.path}
                className={`flex flex-col items-center p-2 relative ${
                  isActive 
                    ? (isAdmin ? 'text-red-600 dark:text-red-400' : (isMerchant ? 'text-accent-600 dark:text-accent-400' : 'text-primary-600 dark:text-primary-400'))
                    : 'text-gray-600 dark:text-gray-400'
                }`}
              >
                <Icon className="w-6 h-6" />
                <span className="text-xs mt-1">{item.label}</span>
                {hasNotification && (
                  <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
                )}
              </Link>
            );
          })}
        </div>
      </nav>

      {/* Desktop Sidebar Navigation */}
      <aside className="hidden md:fixed md:left-0 md:top-0 md:bottom-0 md:w-64 md:bg-white/95 md:dark:bg-gray-800/95 md:backdrop-blur-md md:border-r md:border-gray-200 md:dark:border-gray-700 md:flex md:flex-col md:z-30">
        <nav className="flex-1 p-4 pt-6 space-y-2">
          {navItems.map((item) => {
            const Icon = item.icon;
            const isActive = location.pathname === item.path;
            const hasNotification = false; // No notifications for current nav items
            
            return (
              <Link
                key={item.path}
                to={item.path}
                className={`flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-200 relative ${
                  isActive
                    ? (isAdmin ? 'bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400' : (isMerchant ? 'bg-accent-50 dark:bg-accent-900/20 text-accent-600 dark:text-accent-400' : 'bg-primary-50 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400'))
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
              >
                <Icon className="w-5 h-5" />
                <span className="font-medium">{item.label}</span>
                {hasNotification && (
                  <span className="absolute top-2 left-8 w-3 h-3 bg-red-500 rounded-full"></span>
                )}
              </Link>
            );
          })}
        </nav>
      </aside>

      {/* Floating Chat Button - Only for students and not on messages page */}
      {!isAdmin && !isMerchant && currentUser && location.pathname !== '/messages' && (
        <FloatingChat position="bottom-right" />
      )}
    </div>
  );
};

export default MessagesLayout;
