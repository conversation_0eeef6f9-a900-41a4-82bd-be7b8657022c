import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import App from './App.tsx';
import ErrorBoundary from './components/ErrorBoundary.tsx';
import './index.css';

// Initialize Firebase (this will run the code in firebase/config.ts)
import './firebase/config';

// Initialize console filtering to reduce third-party noise
import { initializeConsoleFiltering, filterStripeErrors } from './utils/consoleFilter';
initializeConsoleFiltering();
filterStripeErrors();

// Initialize Sentry for error tracking and performance monitoring
// import { initSentry } from './utils/sentry';
// import { initializeSecurity } from './utils/security';
// import { setupGlobalErrorHandling } from './utils/errorHandler';
// import { initPerformanceMonitoring } from './utils/performance';
// import { initializeMonitoring } from './utils/monitoring';


// Initialize security measures
// initializeSecurity();

// Initialize global error handling
// setupGlobalErrorHandling();

// Initialize Sentry
// initSentry();

// Initialize performance monitoring
// initPerformanceMonitoring();

// Initialize comprehensive monitoring and alerting
// initializeMonitoring({
//   environment: import.meta.env.MODE || 'development',
//   sampleRate: import.meta.env.PROD ? 0.1 : 1.0,
//   enableErrorTracking: true,
//   enablePerformanceTracking: true,
//   enableUserTracking: import.meta.env.PROD,
//   enableAPIMonitoring: true,
//   enableCustomMetrics: true,
// });

// Clear any existing service workers in development
if (import.meta.env.DEV && 'serviceWorker' in navigator) {
  navigator.serviceWorker.getRegistrations().then(function(registrations) {
    for(const registration of registrations) {
      registration.unregister();
    }
  });
}

// Service worker registration is now handled by vite-plugin-pwa
// The plugin will automatically register the service worker in production

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <ErrorBoundary>
      <App />
    </ErrorBoundary>
  </StrictMode>
);
