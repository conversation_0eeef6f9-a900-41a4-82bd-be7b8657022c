import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import App from './App.tsx';
import ErrorBoundary from './components/ErrorBoundary.tsx';
import './index.css';

// Initialize Firebase (this will run the code in firebase/config.ts)
console.log('🔥 Initializing Firebase...');
try {
  import('./firebase/config');
  console.log('✅ Firebase config imported successfully');
} catch (error) {
  console.error('❌ Firebase config import failed:', error);
}

// Initialize console filtering to reduce third-party noise
import { initializeConsoleFiltering, filterStripeErrors } from './utils/consoleFilter';
initializeConsoleFiltering();
filterStripeErrors();

// Initialize Sentry for error tracking and performance monitoring
// import { initSentry } from './utils/sentry';
// import { initializeSecurity } from './utils/security';
// import { setupGlobalErrorHandling } from './utils/errorHandler';
// import { initPerformanceMonitoring } from './utils/performance';
// import { initializeMonitoring } from './utils/monitoring';


// Initialize security measures
// initializeSecurity();

// Initialize global error handling
// setupGlobalErrorHandling();

// Initialize Sentry
// initSentry();

// Initialize performance monitoring
// initPerformanceMonitoring();

// Initialize comprehensive monitoring and alerting
// initializeMonitoring({
//   environment: import.meta.env.MODE || 'development',
//   sampleRate: import.meta.env.PROD ? 0.1 : 1.0,
//   enableErrorTracking: true,
//   enablePerformanceTracking: true,
//   enableUserTracking: import.meta.env.PROD,
//   enableAPIMonitoring: true,
//   enableCustomMetrics: true,
// });

// Clear any existing service workers in development
if (import.meta.env.DEV && 'serviceWorker' in navigator) {
  navigator.serviceWorker.getRegistrations().then(function(registrations) {
    for(const registration of registrations) {
      registration.unregister();
    }
  });
}

// Service worker registration is now handled by vite-plugin-pwa
// The plugin will automatically register the service worker in production

console.log('🚀 Starting React app...');
console.log('📊 Environment variables check:', {
  NODE_ENV: import.meta.env.MODE,
  FIREBASE_API_KEY: import.meta.env.VITE_FIREBASE_API_KEY ? '✅ Present' : '❌ Missing',
  FIREBASE_PROJECT_ID: import.meta.env.VITE_FIREBASE_PROJECT_ID ? '✅ Present' : '❌ Missing',
  STRIPE_KEY: import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY ? '✅ Present' : '❌ Missing'
});

const rootElement = document.getElementById('root');
if (!rootElement) {
  console.error('❌ Root element not found!');
  throw new Error('Root element not found');
}

console.log('✅ Root element found, creating React root...');

try {
  createRoot(rootElement).render(
    <StrictMode>
      <ErrorBoundary>
        <App />
      </ErrorBoundary>
    </StrictMode>
  );
  console.log('✅ React app rendered successfully');
} catch (error) {
  console.error('❌ React app render failed:', error);
}
